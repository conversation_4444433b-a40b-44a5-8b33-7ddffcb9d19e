//
//  LNVideoChatVC+Request.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

import UIKit
import JKSwiftExtension

extension LNVideoChatVC {

    
    // 远端用户加入后，开始计时，并开始计费
    func authorJoinVideoStartTimer() {
        
        // 开始计时
        self.lnTimer = LNSwiftTimer(interval: DispatchTimeInterval.seconds(1), repeats: true) {
            [weak self] _ in
            guard let `self` = self else {return}
               
            self.videoDuration += 1
            // 每隔1min扣费1次
            if self.videoDuration % 60 == 0 {
                self.postVideoDeduct()
            }
            self.timeLbl.text(JKPOP.getFormatPlayTime(seconds: self.videoDuration, type: JKTimeBarType.hour))
        }
        self.lnTimer?.start()
        
        
    }
    
    
    func postVideoDeduct(isFirst: Bool = false) {
        
        let dic = [
            "channelId": self.channel,
            "firstFlag": isFirst ? "1" : "0"
        ]
        
        /*
         // 余额充足，刷新用户钻石数，
         // 扣费失败，直接关闭频道
         // 余额不足，提示充值
         */
        NetWorkRequest(LNApiVideo.postVideoDeduct(par: dic)) { result in
            
            
            
        } failure: { error in
            
        }

        
    }

}
