import UIKit
import Moya


public enum LNApiProfile {
    /// 用户信息
    case userDetail(par: [String: Any])
    /// 编辑用户
    case userUpdate(par: [String: Any])
    /// VIP价格
    case vipPrice(par: [String: Any])
    /// 充值价格
    case rechargePrice(par: [String: Any])
    /// 黑名单列表
    case blackList(par: [String: Any])
    /// 拉黑用户
    case blackUser(par: [String: Any])
    /// 取消拉黑
    case removeBlack(par: [String: Any])
    /// 充值记录
    case rechargeRecords(par: [String: Any])
    /// 钻石历史记录
    case diamondRecords(par: [String: Any])
    /// 免打扰获取状态
    case disturbStatus
    /// 免打扰设置
    case disturbSwitch(par: [String: Any])
    /// 文件上传
    case fileUpload(par: [String: Any])
    /// 是否关注
    case followFlag(par: [String: Any])
    /// 礼物列表
    case giftList(par: [String: Any])
    /// 国家列表
    case countryList
    /// 谁喜欢我
    case whoSeeMe(par: [String: Any])
    /// 用户礼物列表
    case userGiftList(par: [String: Any])
    /// 获取融云IM Token
    case getRongCloudToken(par: [String: Any])
    // MARK: - 互动功能
    /// 送礼物
    case giftGive(par: [String: Any])
    /// 关注用户
    case followUser(par: [String: Any])
    /// 取消关注
    case unfollowUser(par: [String: Any])

    // MARK: - 订单相关
    /// 充值价格列表
    case rechargePriceList(par: [String: Any])
    /// 支付方式列表
    case paymentMethodList(par: [String: Any])
    /// 创建订单
    case createOrder(par: [String: Any])
    /// 检查订单
    case checkOrder(par: [String: Any])
}

extension LNApiProfile: Moya.TargetType {
    public var baseURL: URL {
        switch self {
        case .vipPrice, .rechargePrice, .rechargeRecords, .rechargePriceList, .paymentMethodList, .createOrder, .checkOrder:
            return URL(string: apiURL + "/ks-order/")!
        case .fileUpload:
            return URL(string: apiURL + "/materesource/")!
        case .giftList, .giftGive, .getRongCloudToken:
            return URL(string: apiURL + "/ks-mikchat/")!
        default:
            return URL(string: apiURL + "/blade-auth/")!
        }
    }

    public var path: String {
        var baseURL = ""
        var p: [String: Any]?
        switch self {
        case .userDetail(let par):
            baseURL = "user/detail"
            p = par
        case .userUpdate(let par):
            baseURL = "user/update"
            p = par
        case .vipPrice(let par):
            baseURL = "order/price"
            p = par
        case .rechargePrice(let par):
            baseURL = "order/price"
            p = par
        case .blackList(let par):
            baseURL = "user/black/list"
            p = par
        case .blackUser(let par):
            baseURL = "user/black"
            p = par
        case .removeBlack(let par):
            baseURL = "user/black/remove"
            p = par
        case .rechargeRecords(let par):
            baseURL = "order/recharge/records"
            p = par
        case .diamondRecords(let par):
            baseURL = "diamond/record/page"
            p = par
        case .disturbStatus:
            baseURL = "user/disturb/status"
        case .disturbSwitch(let par):
            baseURL = "user/disturb/switch"
            p = par
        case .fileUpload(let par):
            baseURL = "resource/upload"
            p = par
        case .followFlag(let par):
            baseURL = "user/follow/flag"
            p = par
        case .giftList(let par):
            baseURL = "gift/list"
            p = par
        case .countryList:
            baseURL = "auth/country/list"
        case .whoSeeMe(let par):
            baseURL = "user/who/see/me"
            p = par
        case .userGiftList(let par):
            baseURL = "diamond/record/page"
            p = par
        case .giftGive(let par):
            baseURL = "gift/give"
            p = par
        case .followUser(let par):
            baseURL = "user/follow/mind"
            p = par
        case .unfollowUser(let par):
            baseURL = "user/unfollow/mind"
            p = par
        case .rechargePriceList(let par):
            baseURL = "order/price"
            p = par
        case .paymentMethodList(let par):
            baseURL = "order/price/paymethod"
            p = par
        case .createOrder(let par):
            baseURL = "order/save/new"
            p = par
        case .checkOrder(let par):
            baseURL = "order/pay/check"
            p = par
        case .getRongCloudToken(let par):
            baseURL = "message/rongcloud/token"
            p = par
        }

        if let parDict = p {
            var namedPaird = [String]()
            for(key, value) in parDict {
                if let valueStr = value as? String, let utf8Str = valueStr.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                    // 处理url上包含中文时，进行编码
                    namedPaird.append("\(key)=\(utf8Str)")
                } else {
                    namedPaird.append("\(key)=\(value)")
                }
            }
            let signedString = namedPaird.joined(separator:"&")
            return baseURL + "?" + signedString
        } else {
            return baseURL
        }
    }
        
    public var method: Moya.Method {
        switch self {
        case .userDetail, .followFlag, .giftList, .countryList, .whoSeeMe, .userGiftList, .rechargePriceList, .paymentMethodList, .checkOrder, .disturbStatus:
            return .get
        case .userUpdate, .vipPrice, .rechargePrice, .blackList, .blackUser, .removeBlack, .disturbSwitch, .rechargeRecords, .diamondRecords, .fileUpload, .giftGive, .followUser, .unfollowUser, .createOrder, .getRongCloudToken:
            return .post
        }
    }
    
    /// 这个是做单元测试模拟的数据，必须要实现，只在单元测试文件中有作用
    public var sampleData: Data {
        return "".data(using: String.Encoding.utf8)!
    }
    
    public var task: Task {
        var params: [String: Any]? = nil

        switch self {
        case .userUpdate(let par),
             .vipPrice(let par),
             .rechargePrice(let par),
             .blackList(let par),
             .rechargeRecords(let par),
             .diamondRecords(let par),
             .blackUser(let par),
             .removeBlack(let par),
             .disturbSwitch(let par),
             .fileUpload(let par),
             .giftGive(let par),
             .followUser(let par),
             .unfollowUser(let par),
             .createOrder(let par),
             .getRongCloudToken(let par):
            params = par
        case .userDetail(_), .followFlag(_), .giftList(_), .countryList, .whoSeeMe(_), .userGiftList(_), .rechargePriceList(_), .paymentMethodList(_), .checkOrder(_), .disturbStatus:
            // GET请求的参数已经在path中处理了
            return .requestPlain
        }
        if let params = params {
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        }
        return .requestPlain
    }
    
    public var headers: [String: String]? {
        
        
        return LNNetApiTool.networkHeaders()
    }
}
