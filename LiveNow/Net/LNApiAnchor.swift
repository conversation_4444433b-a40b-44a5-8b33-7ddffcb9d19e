import UIKit
import Moya


public enum LNApiAnchor {
    // MARK: - 主播列表 Home
    /// 推荐主播列表
    case recommendList(par: [String: Any])
    /// 热门主播列表
    case popularList(par: [String: Any])
    /// 新主播列表
    case newList(par: [String: Any])
    /// 关注主播列表
    case followList(par: [String: Any])

    // MARK: - 主播详情
    /// 主播详情
    case anchorDetail(par: [String: Any])
    /// 主播简要详情
    case anchorLitDetail(par: [String: Any])
    /// 随机主播
    case randomAnchor(par: [String: Any])

    // MARK: - 视频通话
    /// 生成视频频道
    case videoChannel(par: [String: Any])
    /// 获取RTC Token
    case rtcToken(par: [String: Any])
    /// 挂断视频
    case videoHangup(par: [String: Any])
    /// 取消视频推送
    case videoPushCancel(par: [String: Any])
    /// 视频扣费
    case videoDeduct(par: [String: Any])
    /// 视频推送
    case videoPush(par: [String: Any])
    /// 来电主播
    case incomingAnchor(par: [String: Any])
}

extension LNApiAnchor: Moya.TargetType {
    public var baseURL: URL {
        return URL(string: apiURL + "/ks-mikchat/")!
    }

    public var path: String {
        var baseURL = ""
        var p: [String: Any]?
        switch self {
        case .recommendList(let par):
            baseURL = "anchor/recommend"
            p = par
        case .popularList(let par):
            baseURL = "anchor/popular"
            p = par
        case .newList(let par):
            baseURL = "anchor/new"
            p = par
        case .followList(let par):
            baseURL = "anchor/follow"
            p = par
        case .anchorDetail(let par):
            baseURL = "anchor/detail"
            p = par
        case .anchorLitDetail(let par):
            baseURL = "anchor/litDetail"
            p = par
        case .randomAnchor(let par):
            baseURL = "video/random/anchor"
            p = par
        case .videoChannel(let par):
            baseURL = "video/channel"
            p = par
        case .rtcToken(let par):
            baseURL = "video/rtc/token"
            p = par
        case .videoHangup(let par):
            baseURL = "video/hangup"
            p = par
        case .videoPushCancel(let par):
            baseURL = "video/push/cancel"
            p = par
        case .videoDeduct(let par):
            baseURL = "video/deduct"
            p = par
        case .videoPush(let par):
            baseURL = "video/push"
            p = par
        case .incomingAnchor(let par):
            baseURL = "video/incoming/anchor"
            p = par
        }

        if let parDict = p {
            var namedPaird = [String]()
            for(key, value) in parDict {
                if let valueStr = value as? String, let utf8Str = valueStr.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                    // 处理url上包含中文时，进行编码
                    namedPaird.append("\(key)=\(utf8Str)")
                } else {
                    namedPaird.append("\(key)=\(value)")
                }
            }
            let signedString = namedPaird.joined(separator:"&")
            return baseURL + "?" + signedString
        } else {
            return baseURL
        }
    }

    public var method: Moya.Method {
        switch self {
        case .anchorDetail, .anchorLitDetail, .randomAnchor, .rtcToken, .incomingAnchor:
            return .get
        case .recommendList, .popularList, .newList, .followList, .videoChannel, .videoHangup, .videoPushCancel, .videoDeduct, .videoPush:
            return .post
        }
    }

    /// 这个是做单元测试模拟的数据，必须要实现，只在单元测试文件中有作用
    public var sampleData: Data {
        return "".data(using: String.Encoding.utf8)!
    }

    public var task: Task {
        var params: [String: Any]? = nil

        switch self {
        case  .recommendList(let par), 
              .popularList(let par), 
              .newList(let par), 
              .followList(let par), 
              .videoChannel(let par),
              .videoHangup(let par),
              .videoPushCancel(let par),
              .videoDeduct(let par),
              .videoPush(let par):
            params = par
        case .anchorDetail(_), .anchorLitDetail(_), .randomAnchor(_), .rtcToken(_), .incomingAnchor(_):
            // GET请求的参数已经在path中处理了
            return .requestPlain
        }
        if let params = params {
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        }
        return .requestPlain
    }

    public var headers: [String: String]? {
        return LNNetApiTool.networkHeaders()
    }
}