//
//  LNCountryModal.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/16.
//

import UIKit
import SnapKit
import JKSwiftExtension

/// 国家数据模型
struct LNCountry {
    let id: Int?                // 国家ID
    let countryName: String?    // 中文名称
    let countryNameEn: String   // 英文名称
    let countryCode: String     // 国家代码

    /// 从JSON数据创建国家对象
    init(json: [String: Any]) {
        self.id = json["id"] as? Int
        self.countryName = json["countryName"] as? String
        self.countryNameEn = json["countryNameEn"] as? String ?? ""
        self.countryCode = json["countryCode"] as? String ?? ""
    }

    /// 用于兼容现有代码的便利初始化器
    init(name: String, code: String) {
        self.id = nil
        self.countryName = nil
        self.countryNameEn = name
        self.countryCode = code
    }

    /// 显示名称（优先显示英文名称）
    var displayName: String {
        return countryNameEn.isEmpty ? (countryName ?? "") : countryNameEn
    }
}

/// 国家列表数据管理
class LNCountryList {
    static let shared = LNCountryList()

    private init() {}

    /// 所有国家列表（从接口获取）
    private var allCountries: [LNCountry] = []

    /// 数据加载状态
    private var isLoading = false
    private var isLoaded = false

    /// 获取国家列表（异步）
    func fetchCountryList(completion: @escaping ([LNCountry]) -> Void, failure: @escaping (Error) -> Void) {
        // 如果已经加载过数据，直接返回
        if isLoaded {
            completion(allCountries)
            return
        }

        // 如果正在加载，避免重复请求
        if isLoading {
            return
        }

        isLoading = true

        // 调用接口获取国家列表
        NetWorkRequest(LNApiProfile.countryList, completion: { [weak self] result in
            guard let self = self else { return }

            self.isLoading = false

            // 解析数据
            if let data = result["data"] as? [[String: Any]] {
                self.allCountries = data.map { LNCountry(json: $0) }
                self.isLoaded = true

                print("LNCountryList: 成功获取 \(self.allCountries.count) 个国家")
                completion(self.allCountries)
            } else {
                print("LNCountryList: 数据解析失败")
                let error = NSError(domain: "LNCountryListError", code: -1, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"])
                failure(error)
            }

        }, failure: { [weak self] error in
            guard let self = self else { return }

            self.isLoading = false
            print("LNCountryList: 网络请求失败 - \(error.localizedDescription)")
            failure(error)
        })
    }

    /// 按英文名称排序的国家列表
    var sortedByEnglishName: [LNCountry] {
        return allCountries.sorted { $0.displayName < $1.displayName }
    }

    /// 根据代码获取英文名称
    func getEnglishNameByCode(_ code: String) -> String? {
        return allCountries.first { $0.countryCode.lowercased() == code.lowercased() }?.displayName
    }

    /// 根据英文名称获取代码
    func getCodeByEnglishName(_ name: String) -> String? {
        return allCountries.first { $0.displayName == name }?.countryCode
    }

    /// 获取已加载的国家列表（同步）
    func getLoadedCountries() -> [LNCountry] {
        return allCountries
    }
}

/// 国家选择弹框模态视图
class LNCountryModal: UIView {

    // MARK: - Properties
    var onCountrySelected: ((LNCountry) -> Void)?
    var onCancel: (() -> Void)?

    private var countries: [LNCountry] = []
    private var filteredCountries: [LNCountry] = []
    private var isSearching: Bool = false
    
    // MARK: - UI Elements
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white
        view.layer.cornerRadius = s(20)
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Select Country"
        label.font = LNFont.medium(18)
        label.textColor = UIColor.label
        label.textAlignment = .center
        return label
    }()
    
    private lazy var searchBar: UISearchBar = {
        let searchBar = UISearchBar()
        searchBar.placeholder = "Search country..."
        searchBar.searchBarStyle = .minimal
        searchBar.delegate = self
        return searchBar
    }()
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor.systemBackground
        tableView.separatorStyle = .singleLine
        tableView.separatorColor = UIColor.separator
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "CountryCell")

        // 添加调试边框
        tableView.layer.borderWidth = 1
        tableView.layer.borderColor = UIColor.red.cgColor

        return tableView
    }()
    
    private lazy var cancelButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Cancel", for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.setTitleColor(UIColor.systemRed, for: .normal)
        button.backgroundColor = UIColor.clear
        button.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupData()
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private Methods
    private func setupData() {
        // 从接口获取国家列表
        LNCountryList.shared.fetchCountryList(completion: { [weak self] countries in
            DispatchQueue.main.async {
                guard let self = self else { return }

                // 按英文名称排序
                self.countries = countries.sorted { $0.displayName < $1.displayName }
                self.filteredCountries = self.countries

                // 重新加载表格数据
                self.tableView.reloadData()

                print("LNCountryModal: 成功加载了 \(self.countries.count) 个国家")
                print("LNCountryModal: 过滤后有 \(self.filteredCountries.count) 个国家")
                if !self.countries.isEmpty {
                    print("LNCountryModal: 第一个国家: \(self.countries[0].displayName) (\(self.countries[0].countryCode))")
                }
            }
        }, failure: { [weak self] error in
            DispatchQueue.main.async {
                guard let self = self else { return }

                print("LNCountryModal: 获取国家列表失败 - \(error.localizedDescription)")
                // 可以在这里显示错误提示
                // 或者使用默认的空列表
                self.countries = []
                self.filteredCountries = []
                self.tableView.reloadData()
            }
        })
    }
    
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)

        containerView.addSubview(titleLabel)
        containerView.addSubview(searchBar)
        containerView.addSubview(tableView)
        containerView.addSubview(cancelButton)
    }
    
    private func setupConstraints() {
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(s(600))
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
            make.height.equalTo(s(25))
        }
        
        searchBar.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview().inset(s(16))
            make.height.equalTo(s(44))
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview()
            make.bottom.equalTo(cancelButton.snp.top).offset(-s(10))
        }
        
        cancelButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(s(50))
            make.bottom.equalToSuperview().offset(-s(40))
        }
    }
    
    // MARK: - Actions
    @objc private func backgroundTapped() {
        dismiss()
        onCancel?()
    }
    
    @objc private func cancelTapped() {
        dismiss()
        onCancel?()
    }
    
    private func filterCountries(with searchText: String) {
        if searchText.isEmpty {
            filteredCountries = countries
            isSearching = false
        } else {
            filteredCountries = countries.filter { country in
                country.name.lowercased().contains(searchText.lowercased())
            }
            isSearching = true
        }
        tableView.reloadData()
    }
}

// MARK: - UISearchBarDelegate
extension LNCountryModal: UISearchBarDelegate {
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {
        filterCountries(with: searchText)
    }
    
    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension LNCountryModal: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        print("LNCountryModal: numberOfRowsInSection 返回 \(filteredCountries.count)")
        return filteredCountries.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CountryCell", for: indexPath)
        let country = filteredCountries[indexPath.row]
        let text = "\(country.name) (\(country.code))"
        cell.textLabel?.text = text
        cell.textLabel?.font = LNFont.medium(16)
        cell.textLabel?.textColor = UIColor.label
        cell.backgroundColor = UIColor.systemBackground
        cell.selectionStyle = .default

        print("LNCountryModal: 创建单元格 \(indexPath.row): \(text)")
        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let selectedCountry = filteredCountries[indexPath.row]

        dismiss()
        onCountrySelected?(selectedCountry)
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return s(50)
    }
}

// MARK: - Public Methods
extension LNCountryModal {
    /// 显示国家选择弹框
    /// - Parameter parentView: 父视图
    func show(in parentView: UIView) {
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 强制布局以获取正确的frame
        self.layoutIfNeeded()

        // 确保表格视图数据已加载
        tableView.reloadData()
        print("LNCountryModal: show 方法中重新加载表格数据")

        // 设置初始状态
        backgroundView.alpha = 0
        containerView.transform = CGAffineTransform(translationX: 0, y: s(600))

        // 显示动画
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut) {
            self.backgroundView.alpha = 1
            self.containerView.transform = .identity
        }
    }
    
    /// 隐藏国家选择弹框
    private func dismiss() {
        // 隐藏键盘
        searchBar.resignFirstResponder()
        
        UIView.animate(withDuration: 0.25, animations: {
            self.backgroundView.alpha = 0
            self.containerView.transform = CGAffineTransform(translationX: 0, y: s(600))
        }) { _ in
            self.removeFromSuperview()
        }
    }
}
