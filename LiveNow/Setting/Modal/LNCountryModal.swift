//
//  LNCountryModal.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/16.
//

import UIKit
import SnapKit
import JKSwiftExtension

/// 国家数据模型
struct LNCountry {
    let name: String    // 英文名称
    let code: String    // 国家代码
}

/// 国家列表数据管理
class LNCountryList {
    static let shared = LNCountryList()

    private init() {}

    /// 所有国家列表
    let allCountries: [LNCountry] = [
        LNCountry(name: "Afghanistan", code: "AF"),
        LNCountry(name: "Albania", code: "AL"),
        LNCountry(name: "Algeria", code: "DZ"),
        LNCountry(name: "Andorra", code: "AD"),
        LNCountry(name: "Angola", code: "AO"),
        LNCountry(name: "Antigua and Barbuda", code: "AG"),
        LNCountry(name: "Argentina", code: "AR"),
        LNCountry(name: "Armenia", code: "AM"),
        LNCountry(name: "Australia", code: "AU"),
        LNCountry(name: "Austria", code: "AT"),
        LNCountry(name: "Azerbaijan", code: "AZ"),
        LNCountry(name: "Bahamas", code: "BS"),
        LNCountry(name: "Bahrain", code: "BH"),
        LNCountry(name: "Bangladesh", code: "BD"),
        LNCountry(name: "Barbados", code: "BB"),
        LNCountry(name: "Belarus", code: "BY"),
        LNCountry(name: "Belgium", code: "BE"),
        LNCountry(name: "Belize", code: "BZ"),
        LNCountry(name: "Benin", code: "BJ"),
        LNCountry(name: "Bhutan", code: "BT"),
        LNCountry(name: "Bolivia", code: "BO"),
        LNCountry(name: "Bosnia and Herzegovina", code: "BA"),
        LNCountry(name: "Botswana", code: "BW"),
        LNCountry(name: "Brazil", code: "BR"),
        LNCountry(name: "Brunei", code: "BN"),
        LNCountry(name: "Bulgaria", code: "BG"),
        LNCountry(name: "Burkina Faso", code: "BF"),
        LNCountry(name: "Burundi", code: "BI"),
        LNCountry(name: "Cambodia", code: "KH"),
        LNCountry(name: "Cameroon", code: "CM"),
        LNCountry(name: "Canada", code: "CA"),
        LNCountry(name: "Cape Verde", code: "CV"),
        LNCountry(name: "Central African Republic", code: "CF"),
        LNCountry(name: "Chad", code: "TD"),
        LNCountry(name: "Chile", code: "CL"),
        LNCountry(name: "China", code: "CN"),
        LNCountry(name: "Colombia", code: "CO"),
        LNCountry(name: "Comoros", code: "KM"),
        LNCountry(name: "Congo (Brazzaville)", code: "CG"),
        LNCountry(name: "Congo (Kinshasa)", code: "CD"),
        LNCountry(name: "Costa Rica", code: "CR"),
        LNCountry(name: "Côte d'Ivoire", code: "CI"),
        LNCountry(name: "Croatia", code: "HR"),
        LNCountry(name: "Cuba", code: "CU"),
        LNCountry(name: "Cyprus", code: "CY"),
        LNCountry(name: "Czech Republic", code: "CZ"),
        LNCountry(name: "Denmark", code: "DK"),
        LNCountry(name: "Djibouti", code: "DJ"),
        LNCountry(name: "Dominica", code: "DM"),
        LNCountry(name: "Dominican Republic", code: "DO"),
        LNCountry(name: "Ecuador", code: "EC"),
        LNCountry(name: "Egypt", code: "EG"),
        LNCountry(name: "El Salvador", code: "SV"),
        LNCountry(name: "Equatorial Guinea", code: "GQ"),
        LNCountry(name: "Eritrea", code: "ER"),
        LNCountry(name: "Estonia", code: "EE"),
        LNCountry(name: "Ethiopia", code: "ET"),
        LNCountry(name: "Fiji", code: "FJ"),
        LNCountry(name: "Finland", code: "FI"),
        LNCountry(name: "France", code: "FR"),
        LNCountry(name: "Gabon", code: "GA"),
        LNCountry(name: "Gambia", code: "GM"),
        LNCountry(name: "Georgia", code: "GE"),
        LNCountry(name: "Germany", code: "DE"),
        LNCountry(name: "Ghana", code: "GH"),
        LNCountry(name: "Greece", code: "GR"),
        LNCountry(name: "Grenada", code: "GD"),
        LNCountry(name: "Guatemala", code: "GT"),
        LNCountry(name: "Guinea", code: "GN"),
        LNCountry(name: "Guinea-Bissau", code: "GW"),
        LNCountry(name: "Guyana", code: "GY"),
        LNCountry(name: "Haiti", code: "HT"),
        LNCountry(name: "Honduras", code: "HN"),
        LNCountry(name: "Hungary", code: "HU"),
        LNCountry(name: "Iceland", code: "IS"),
        LNCountry(name: "India", code: "IN"),
        LNCountry(name: "Indonesia", code: "ID"),
        LNCountry(name: "Iran", code: "IR"),
        LNCountry(name: "Iraq", code: "IQ"),
        LNCountry(name: "Ireland", code: "IE"),
        LNCountry(name: "Israel", code: "IL"),
        LNCountry(name: "Italy", code: "IT"),
        LNCountry(name: "Jamaica", code: "JM"),
        LNCountry(name: "Japan", code: "JP"),
        LNCountry(name: "Jordan", code: "JO"),
        LNCountry(name: "Kazakhstan", code: "KZ"),
        LNCountry(name: "Kenya", code: "KE"),
        LNCountry(name: "Kiribati", code: "KI"),
        LNCountry(name: "Korea, North", code: "KP"),
        LNCountry(name: "Korea, South", code: "KR"),
        LNCountry(name: "Kuwait", code: "KW"),
        LNCountry(name: "Kyrgyzstan", code: "KG"),
        LNCountry(name: "Laos", code: "LA"),
        LNCountry(name: "Latvia", code: "LV"),
        LNCountry(name: "Lebanon", code: "LB"),
        LNCountry(name: "Lesotho", code: "LS"),
        LNCountry(name: "Liberia", code: "LR"),
        LNCountry(name: "Libya", code: "LY"),
        LNCountry(name: "Liechtenstein", code: "LI"),
        LNCountry(name: "Lithuania", code: "LT"),
        LNCountry(name: "Luxembourg", code: "LU"),
        LNCountry(name: "Madagascar", code: "MG"),
        LNCountry(name: "Malawi", code: "MW"),
        LNCountry(name: "Malaysia", code: "MY"),
        LNCountry(name: "Maldives", code: "MV"),
        LNCountry(name: "Mali", code: "ML"),
        LNCountry(name: "Malta", code: "MT"),
        LNCountry(name: "Marshall Islands", code: "MH"),
        LNCountry(name: "Mauritania", code: "MR"),
        LNCountry(name: "Mauritius", code: "MU"),
        LNCountry(name: "Mexico", code: "MX"),
        LNCountry(name: "Micronesia", code: "FM"),
        LNCountry(name: "Moldova", code: "MD"),
        LNCountry(name: "Monaco", code: "MC"),
        LNCountry(name: "Mongolia", code: "MN"),
        LNCountry(name: "Montenegro", code: "ME"),
        LNCountry(name: "Morocco", code: "MA"),
        LNCountry(name: "Mozambique", code: "MZ"),
        LNCountry(name: "Myanmar", code: "MM"),
        LNCountry(name: "Namibia", code: "NA"),
        LNCountry(name: "Nauru", code: "NR"),
        LNCountry(name: "Nepal", code: "NP"),
        LNCountry(name: "Netherlands", code: "NL"),
        LNCountry(name: "New Zealand", code: "NZ"),
        LNCountry(name: "Nicaragua", code: "NI"),
        LNCountry(name: "Niger", code: "NE"),
        LNCountry(name: "Nigeria", code: "NG"),
        LNCountry(name: "North Macedonia", code: "MK"),
        LNCountry(name: "Norway", code: "NO"),
        LNCountry(name: "Oman", code: "OM"),
        LNCountry(name: "Pakistan", code: "PK"),
        LNCountry(name: "Palau", code: "PW"),
        LNCountry(name: "Panama", code: "PA"),
        LNCountry(name: "Papua New Guinea", code: "PG"),
        LNCountry(name: "Paraguay", code: "PY"),
        LNCountry(name: "Peru", code: "PE"),
        LNCountry(name: "Philippines", code: "PH"),
        LNCountry(name: "Poland", code: "PL"),
        LNCountry(name: "Portugal", code: "PT"),
        LNCountry(name: "Qatar", code: "QA"),
        LNCountry(name: "Romania", code: "RO"),
        LNCountry(name: "Russia", code: "RU"),
        LNCountry(name: "Rwanda", code: "RW"),
        LNCountry(name: "Saint Kitts and Nevis", code: "KN"),
        LNCountry(name: "Saint Lucia", code: "LC"),
        LNCountry(name: "Saint Vincent and the Grenadines", code: "VC"),
        LNCountry(name: "Samoa", code: "WS"),
        LNCountry(name: "San Marino", code: "SM"),
        LNCountry(name: "São Tomé and Príncipe", code: "ST"),
        LNCountry(name: "Saudi Arabia", code: "SA"),
        LNCountry(name: "Senegal", code: "SN"),
        LNCountry(name: "Serbia", code: "RS"),
        LNCountry(name: "Seychelles", code: "SC"),
        LNCountry(name: "Sierra Leone", code: "SL"),
        LNCountry(name: "Singapore", code: "SG"),
        LNCountry(name: "Slovakia", code: "SK"),
        LNCountry(name: "Slovenia", code: "SI"),
        LNCountry(name: "Solomon Islands", code: "SB"),
        LNCountry(name: "Somalia", code: "SO"),
        LNCountry(name: "South Africa", code: "ZA"),
        LNCountry(name: "South Sudan", code: "SS"),
        LNCountry(name: "Spain", code: "ES"),
        LNCountry(name: "Sri Lanka", code: "LK"),
        LNCountry(name: "Sudan", code: "SD"),
        LNCountry(name: "Suriname", code: "SR"),
        LNCountry(name: "Sweden", code: "SE"),
        LNCountry(name: "Switzerland", code: "CH"),
        LNCountry(name: "Syria", code: "SY"),
        LNCountry(name: "Tajikistan", code: "TJ"),
        LNCountry(name: "Tanzania", code: "TZ"),
        LNCountry(name: "Thailand", code: "TH"),
        LNCountry(name: "Timor-Leste", code: "TL"),
        LNCountry(name: "Togo", code: "TG"),
        LNCountry(name: "Tonga", code: "TO"),
        LNCountry(name: "Trinidad and Tobago", code: "TT"),
        LNCountry(name: "Tunisia", code: "TN"),
        LNCountry(name: "Turkey", code: "TR"),
        LNCountry(name: "Turkmenistan", code: "TM"),
        LNCountry(name: "Tuvalu", code: "TV"),
        LNCountry(name: "Uganda", code: "UG"),
        LNCountry(name: "Ukraine", code: "UA"),
        LNCountry(name: "United Arab Emirates", code: "AE"),
        LNCountry(name: "United Kingdom", code: "GB"),
        LNCountry(name: "United States", code: "US"),
        LNCountry(name: "Uruguay", code: "UY"),
        LNCountry(name: "Uzbekistan", code: "UZ"),
        LNCountry(name: "Vanuatu", code: "VU"),
        LNCountry(name: "Venezuela", code: "VE"),
        LNCountry(name: "Vietnam", code: "VN"),
        LNCountry(name: "Yemen", code: "YE"),
        LNCountry(name: "Zambia", code: "ZM"),
        LNCountry(name: "Zimbabwe", code: "ZW")
    ]

    /// 按英文名称排序的国家列表
    lazy var sortedByEnglishName: [LNCountry] = {
        return allCountries.sorted { $0.name < $1.name }
    }()

    /// 根据代码获取英文名称
    func getEnglishNameByCode(_ code: String) -> String? {
        return allCountries.first { $0.code.lowercased() == code.lowercased() }?.name
    }

    /// 根据英文名称获取代码
    func getCodeByEnglishName(_ name: String) -> String? {
        return allCountries.first { $0.name == name }?.code
    }
}

/// 国家选择弹框模态视图
class LNCountryModal: UIView {

    // MARK: - Properties
    var onCountrySelected: ((LNCountry) -> Void)?
    var onCancel: (() -> Void)?

    private var countries: [LNCountry] = []
    private var filteredCountries: [LNCountry] = []
    private var isSearching: Bool = false
    
    // MARK: - UI Elements
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white
        view.layer.cornerRadius = s(20)
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Select Country"
        label.font = LNFont.medium(18)
        label.textColor = UIColor.label
        label.textAlignment = .center
        return label
    }()
    
    private lazy var searchBar: UISearchBar = {
        let searchBar = UISearchBar()
        searchBar.placeholder = "Search country..."
        searchBar.searchBarStyle = .minimal
        searchBar.delegate = self
        return searchBar
    }()
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor.systemBackground
        tableView.separatorStyle = .singleLine
        tableView.separatorColor = UIColor.separator
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "CountryCell")

        // 添加调试边框
        tableView.layer.borderWidth = 1
        tableView.layer.borderColor = UIColor.red.cgColor

        return tableView
    }()
    
    private lazy var cancelButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Cancel", for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.setTitleColor(UIColor.systemRed, for: .normal)
        button.backgroundColor = UIColor.clear
        button.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupData()
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private Methods
    private func setupData() {
        // 使用完整的国家列表，按英文名称排序
        countries = LNCountryList.shared.sortedByEnglishName
        filteredCountries = countries

        print("LNCountryModal: 加载了 \(countries.count) 个国家")
        print("LNCountryModal: 过滤后有 \(filteredCountries.count) 个国家")
        if !countries.isEmpty {
            print("LNCountryModal: 第一个国家: \(countries[0].name) (\(countries[0].code))")
        }
    }
    
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)

        containerView.addSubview(titleLabel)
        containerView.addSubview(searchBar)
        containerView.addSubview(tableView)
        containerView.addSubview(cancelButton)
    }
    
    private func setupConstraints() {
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(s(600))
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.right.equalToSuperview().inset(s(20))
            make.height.equalTo(s(25))
        }
        
        searchBar.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview().inset(s(16))
            make.height.equalTo(s(44))
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(searchBar.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview()
            make.bottom.equalTo(cancelButton.snp.top).offset(-s(10))
        }
        
        cancelButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(s(50))
            make.bottom.equalToSuperview().offset(-s(40))
        }
    }
    
    // MARK: - Actions
    @objc private func backgroundTapped() {
        dismiss()
        onCancel?()
    }
    
    @objc private func cancelTapped() {
        dismiss()
        onCancel?()
    }
    
    private func filterCountries(with searchText: String) {
        if searchText.isEmpty {
            filteredCountries = countries
            isSearching = false
        } else {
            filteredCountries = countries.filter { country in
                country.name.lowercased().contains(searchText.lowercased())
            }
            isSearching = true
        }
        tableView.reloadData()
    }
}

// MARK: - UISearchBarDelegate
extension LNCountryModal: UISearchBarDelegate {
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {
        filterCountries(with: searchText)
    }
    
    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
    }
}

// MARK: - UITableViewDataSource & UITableViewDelegate
extension LNCountryModal: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        print("LNCountryModal: numberOfRowsInSection 返回 \(filteredCountries.count)")
        return filteredCountries.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "CountryCell", for: indexPath)
        let country = filteredCountries[indexPath.row]
        let text = "\(country.name) (\(country.code))"
        cell.textLabel?.text = text
        cell.textLabel?.font = LNFont.medium(16)
        cell.textLabel?.textColor = UIColor.label
        cell.backgroundColor = UIColor.systemBackground
        cell.selectionStyle = .default

        print("LNCountryModal: 创建单元格 \(indexPath.row): \(text)")
        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let selectedCountry = filteredCountries[indexPath.row]

        dismiss()
        onCountrySelected?(selectedCountry)
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return s(50)
    }
}

// MARK: - Public Methods
extension LNCountryModal {
    /// 显示国家选择弹框
    /// - Parameter parentView: 父视图
    func show(in parentView: UIView) {
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 强制布局以获取正确的frame
        self.layoutIfNeeded()

        // 确保表格视图数据已加载
        tableView.reloadData()
        print("LNCountryModal: show 方法中重新加载表格数据")

        // 设置初始状态
        backgroundView.alpha = 0
        containerView.transform = CGAffineTransform(translationX: 0, y: s(600))

        // 显示动画
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut) {
            self.backgroundView.alpha = 1
            self.containerView.transform = .identity
        }
    }
    
    /// 隐藏国家选择弹框
    private func dismiss() {
        // 隐藏键盘
        searchBar.resignFirstResponder()
        
        UIView.animate(withDuration: 0.25, animations: {
            self.backgroundView.alpha = 0
            self.containerView.transform = CGAffineTransform(translationX: 0, y: s(600))
        }) { _ in
            self.removeFromSuperview()
        }
    }
}
