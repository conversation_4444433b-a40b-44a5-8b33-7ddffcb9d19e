//
//  LNAvatarModal.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/16.
//

import UIKit
import SnapKit
import JKSwiftExtension

/// 头像选择弹框模态视图
class LNAvatarModal: UIView {
    
    // MARK: - Properties
    var onSelectFromAlbum: (() -> Void)?
    var onTakePhoto: (() -> Void)?
    var onCancel: (() -> Void)?
    
    // MARK: - UI Elements
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        let tap = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white
        view.layer.cornerRadius = s(20)
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var selectFromAlbumButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Select From Album", for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.setTitleColor(UIColor.label, for: .normal)
        button.backgroundColor = UIColor.clear
        button.addTarget(self, action: #selector(selectFromAlbumTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var takePhotoButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Take Photo", for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.setTitleColor(UIColor.label, for: .normal)
        button.backgroundColor = UIColor.clear
        button.addTarget(self, action: #selector(takePhotoTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var separatorLine: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.separator
        return view
    }()
    
    private lazy var cancelButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Cancel", for: .normal)
        button.titleLabel?.font = LNFont.medium(18)
        button.setTitleColor(UIColor.systemRed, for: .normal)
        button.backgroundColor = UIColor.clear
        button.addTarget(self, action: #selector(cancelTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private Methods
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)
        
        containerView.addSubview(selectFromAlbumButton)
        containerView.addSubview(takePhotoButton)
        containerView.addSubview(separatorLine)
        containerView.addSubview(cancelButton)
    }
    
    private func setupConstraints() {
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(s(220))
        }
        
        selectFromAlbumButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(50))
        }
        
        takePhotoButton.snp.makeConstraints { make in
            make.top.equalTo(selectFromAlbumButton.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(s(50))
        }
        
        separatorLine.snp.makeConstraints { make in
            make.top.equalTo(takePhotoButton.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview()
            make.height.equalTo(0.5)
        }
        
        cancelButton.snp.makeConstraints { make in
            make.top.equalTo(separatorLine.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(50))
            make.bottom.lessThanOrEqualToSuperview().offset(-s(40))
        }
    }
    
    // MARK: - Actions
    @objc private func backgroundTapped() {
        dismiss()
        onCancel?()
    }
    
    @objc private func selectFromAlbumTapped() {
        dismiss()
        onSelectFromAlbum?()
    }
    
    @objc private func takePhotoTapped() {
        dismiss()
        onTakePhoto?()
    }
    
    @objc private func cancelTapped() {
        dismiss()
        onCancel?()
    }
}

// MARK: - Public Methods
extension LNAvatarModal {
    /// 显示头像选择弹框
    /// - Parameter parentView: 父视图
    func show(in parentView: UIView) {
        parentView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 强制布局以获取正确的frame
        self.layoutIfNeeded()
        
        // 设置初始状态
        backgroundView.alpha = 0
        containerView.transform = CGAffineTransform(translationX: 0, y: s(220))
        
        // 显示动画
        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseOut) {
            self.backgroundView.alpha = 1
            self.containerView.transform = .identity
        }
    }
    
    /// 隐藏头像选择弹框
    private func dismiss() {
        UIView.animate(withDuration: 0.25, animations: {
            self.backgroundView.alpha = 0
            self.containerView.transform = CGAffineTransform(translationX: 0, y: s(220))
        }) { _ in
            self.removeFromSuperview()
        }
    }
}
