//
//  LNDeleteAccountViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit

/// 删除账号页面
/// 显示说明文字卡片，底部灰色禁用按钮“Submit a cancellation request”。
/// 该版本仅做静态展示与占位交互，后续可接入真实提交流程与风控校验。
class LNDeleteAccountViewController: LNBaseController {

    // 使用白色导航栏
    override var navigationSolidColor: UIColor { return .white }
    override var navigationTitleColor: UIColor { return .label }

    private lazy var scrollView: UIScrollView = {
        let v = UIScrollView()
        v.alwaysBounceVertical = true
        v.keyboardDismissMode = .onDrag
        return v
    }()
    private lazy var contentView = UIView()

    private lazy var infoCard: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.white
        v.layer.cornerRadius = 12
        v.layer.masksToBounds = true
        return v
    }()

    private lazy var infoLabel: UILabel = {
        let l = UILabel()
        l.text = "Fill Out And Submit A Cancellation Request. Our Staff Will Assist You With The Cancellation Process. After You Voluntarily Cancel, We Will Stop Providing Services And Delete Your Personal Information Or Anonymize It In Accordance With Applicable Laws."
        l.font = LNFont.forTextStyle(.body)
        l.textColor = UIColor.label
        l.numberOfLines = 0
        l.adjustsFontForContentSizeCategory = true
        l.setContentHuggingPriority(.required, for: .vertical)
        return l
    }()

    private lazy var submitButton: UIButton = {
        let b = UIButton(type: .system)
        b.setTitle("Submit a cancellation request", for: .normal)
        b.setTitleColor(UIColor.white.withAlphaComponent(0.9), for: .disabled)
        b.backgroundColor = UIColor.systemGray3
        b.isEnabled = false
        b.layer.cornerRadius = 24
        b.layer.masksToBounds = true
        b.accessibilityLabel = "submit_cancel_request"
        b.addTarget(self, action: #selector(submitTapped), for: .touchUpInside)
        return b
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Delete account"
        setupUI()
        setupConstraints()
    }

    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        view.addSubview(submitButton)

        contentView.addSubview(infoCard)
        infoCard.addSubview(infoLabel)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalTo(view.safeAreaLayoutGuide)
            make.bottom.equalTo(submitButton.snp.top).offset(-16)
        }
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        infoCard.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(16))
            make.left.right.equalToSuperview().inset(s(16))
            make.bottom.equalToSuperview().offset(-s(16))
        }
        infoLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(12)
        }

        submitButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(24)
            make.height.equalTo(48)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-24)
        }
    }

    @objc private func submitTapped() {
        let alert = UIAlertController(title: "Notice", message: "Please complete the verification in the future version.", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

