//
//  LNMembershipCenterViewController.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/17.
//

import UIKit
import SnapKit

class LNMembershipCenterViewController: LNBaseController {
    
    // MARK: - Properties
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        scrollView.showsHorizontalScrollIndicator = false
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // VIP卡片容器
    private lazy var vipCardsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = s(12)
        return stackView
    }()
    
    // Bronze VIP卡片
    private lazy var bronzeVipCard: UIView = {
        return createVipCard(
            title: "Bronze VIP",
            coins: "+600",
            originalPrice: "US$12.79",
            currentPrice: "US$12.79",
            isSelected: true,
            backgroundColor: UIColor.hex(hexString: "#FFE066")
        )
    }()
    
    // Silver VIP卡片
    private lazy var silverVipCard: UIView = {
        return createVipCard(
            title: "Silver VIP",
            coins: "+1100",
            originalPrice: "US$24.79",
            currentPrice: "US$9.99",
            isSelected: false,
            backgroundColor: UIColor.hex(hexString: "#E5E5E5")
        )
    }()
    
    // Gold VIP卡片
    private lazy var goldVipCard: UIView = {
        return createVipCard(
            title: "Gold VIP",
            coins: "+2200",
            originalPrice: "US$12.79",
            currentPrice: "US$19.99",
            isSelected: false,
            backgroundColor: UIColor.hex(hexString: "#E5E5E5")
        )
    }()
    
    // Restore Purchase按钮
    private lazy var restorePurchaseButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Restore Purchase", for: .normal)
        button.setTitleColor(UIColor.hex(hexString: "#666666"), for: .normal)
        button.titleLabel?.font = LNFont.regular(14)
        button.addTarget(self, action: #selector(restorePurchaseAction), for: .touchUpInside)
        return button
    }()
    
    // 周费用按钮
    private lazy var weeklyPriceButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("US$4.99 For week", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.layer.cornerRadius = s(25)
        button.clipsToBounds = true
        
        // 添加渐变背景
        let gradientLayer = LNGradient.makePrimaryHorizontalLayer()
        button.layer.insertSublayer(gradientLayer, at: 0)
        
        button.addTarget(self, action: #selector(weeklyPriceAction), for: .touchUpInside)
        return button
    }()
    
    // VIP权益列表
    private lazy var vipBenefitsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = s(16)
        return stackView
    }()
    
    // Check who liked me按钮
    private lazy var checkLikedButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("Check who liked me", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.layer.cornerRadius = s(25)
        button.clipsToBounds = true
        
        // 添加渐变背景
        let gradientLayer = LNGradient.makePrimaryHorizontalLayer()
        button.layer.insertSublayer(gradientLayer, at: 0)
        
        button.addTarget(self, action: #selector(checkLikedAction), for: .touchUpInside)
        return button
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateGradientLayers()
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        title = "Membership center"
        view.backgroundColor = UIColor.hex(hexString: "#F5F5F5")
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        // 添加VIP卡片
        contentView.addSubview(vipCardsStackView)
        vipCardsStackView.addArrangedSubview(bronzeVipCard)
        vipCardsStackView.addArrangedSubview(silverVipCard)
        vipCardsStackView.addArrangedSubview(goldVipCard)
        
        // 添加其他控件
        contentView.addSubview(restorePurchaseButton)
        contentView.addSubview(weeklyPriceButton)
        contentView.addSubview(vipBenefitsStackView)
        contentView.addSubview(checkLikedButton)
        
        // 创建VIP权益项目
        setupVipBenefits()
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.bottom.equalToSuperview()
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        vipCardsStackView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.equalToSuperview().offset(s(16))
            make.right.equalToSuperview().offset(s(-16))
            make.height.equalTo(s(140))
        }
        
        restorePurchaseButton.snp.makeConstraints { make in
            make.top.equalTo(vipCardsStackView.snp.bottom).offset(s(16))
            make.centerX.equalToSuperview()
            make.height.equalTo(s(44))
        }
        
        weeklyPriceButton.snp.makeConstraints { make in
            make.top.equalTo(restorePurchaseButton.snp.bottom).offset(s(20))
            make.left.equalToSuperview().offset(s(16))
            make.right.equalToSuperview().offset(s(-16))
            make.height.equalTo(s(50))
        }
        
        vipBenefitsStackView.snp.makeConstraints { make in
            make.top.equalTo(weeklyPriceButton.snp.bottom).offset(s(30))
            make.left.equalToSuperview().offset(s(16))
            make.right.equalToSuperview().offset(s(-16))
        }
        
        checkLikedButton.snp.makeConstraints { make in
            make.top.equalTo(vipBenefitsStackView.snp.bottom).offset(s(40))
            make.left.equalToSuperview().offset(s(16))
            make.right.equalToSuperview().offset(s(-16))
            make.height.equalTo(s(50))
            make.bottom.equalToSuperview().offset(s(-30))
        }
    }

    // MARK: - Helper Methods

    private func createVipCard(title: String, coins: String, originalPrice: String, currentPrice: String, isSelected: Bool, backgroundColor: UIColor) -> UIView {
        let cardView = UIView()
        cardView.backgroundColor = backgroundColor
        cardView.layer.cornerRadius = s(12)
        cardView.clipsToBounds = true

        // 如果是选中状态，添加边框
        if isSelected {
            cardView.layer.borderWidth = 2
            cardView.layer.borderColor = UIColor.hex(hexString: "#04E798").cgColor
        }

        // 金币图标和数量
        let coinsLabel = UILabel()
        coinsLabel.text = coins
        coinsLabel.font = LNFont.medium(14)
        coinsLabel.textColor = UIColor.hex(hexString: "#04E798")

        let coinIcon = UIImageView()
        coinIcon.image = UIImage(named: "ic_diamond") ?? UIImage(systemName: "circle.fill")
        coinIcon.tintColor = UIColor.hex(hexString: "#04E798")

        // VIP标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = LNFont.bold(16)
        titleLabel.textColor = .black

        // 原价（删除线）
        let originalPriceLabel = UILabel()
        let attributedString = NSMutableAttributedString(string: originalPrice)
        attributedString.addAttribute(.strikethroughStyle, value: NSUnderlineStyle.single.rawValue, range: NSRange(location: 0, length: originalPrice.count))
        originalPriceLabel.attributedText = attributedString
        originalPriceLabel.font = LNFont.regular(12)
        originalPriceLabel.textColor = UIColor.hex(hexString: "#999999")

        // 现价
        let currentPriceLabel = UILabel()
        currentPriceLabel.text = currentPrice
        currentPriceLabel.font = LNFont.bold(14)
        currentPriceLabel.textColor = .black

        // 添加子视图
        cardView.addSubview(coinIcon)
        cardView.addSubview(coinsLabel)
        cardView.addSubview(titleLabel)
        cardView.addSubview(originalPriceLabel)
        cardView.addSubview(currentPriceLabel)

        // 设置约束
        coinIcon.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(12))
            make.left.equalToSuperview().offset(s(12))
            make.width.height.equalTo(s(16))
        }

        coinsLabel.snp.makeConstraints { make in
            make.centerY.equalTo(coinIcon)
            make.left.equalTo(coinIcon.snp.right).offset(s(4))
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(coinIcon.snp.bottom).offset(s(12))
            make.left.equalToSuperview().offset(s(12))
            make.right.equalToSuperview().offset(s(-12))
        }

        originalPriceLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(8))
            make.left.equalToSuperview().offset(s(12))
        }

        currentPriceLabel.snp.makeConstraints { make in
            make.top.equalTo(originalPriceLabel.snp.bottom).offset(s(4))
            make.left.equalToSuperview().offset(s(12))
        }

        return cardView
    }

    private func setupVipBenefits() {
        let benefits = [
            "VIP 权益一",
            "VIP 权益二",
            "VIP 权益三",
            "VIP 权益四",
            "VIP 权益五",
            "VIP 权益六"
        ]

        for benefit in benefits {
            let benefitView = createVipBenefitItem(title: benefit, subtitle: "Free times to send messages")
            vipBenefitsStackView.addArrangedSubview(benefitView)
        }
    }

    private func createVipBenefitItem(title: String, subtitle: String) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .clear

        // 图标
        let iconView = UIImageView()
        iconView.image = UIImage(named: "ic_vip_star") ?? UIImage(systemName: "star.fill")
        iconView.tintColor = UIColor.hex(hexString: "#04E798")

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = LNFont.medium(16)
        titleLabel.textColor = UIColor.hex(hexString: "#04E798")

        // 副标题
        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = LNFont.regular(14)
        subtitleLabel.textColor = UIColor.hex(hexString: "#666666")

        containerView.addSubview(iconView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)

        iconView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(24))
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconView.snp.right).offset(s(12))
            make.top.equalToSuperview()
            make.right.equalToSuperview()
        }

        subtitleLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(s(4))
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        containerView.snp.makeConstraints { make in
            make.height.equalTo(s(50))
        }

        return containerView
    }

    private func updateGradientLayers() {
        // 更新周费用按钮的渐变层
        if let gradientLayer = weeklyPriceButton.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = weeklyPriceButton.bounds
        }

        // 更新检查喜欢按钮的渐变层
        if let gradientLayer = checkLikedButton.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = checkLikedButton.bounds
        }
    }

    // MARK: - Actions

    @objc private func restorePurchaseAction() {
        // 恢复购买逻辑
        print("Restore Purchase tapped")
    }

    @objc private func weeklyPriceAction() {
        // 周费用购买逻辑
        print("Weekly price tapped")
    }

    @objc private func checkLikedAction() {
        // 跳转到"谁喜欢我"页面
        print("Check who liked me tapped")
        let whoLikedMeVC = LNWhoLikedMeViewController()
        whoLikedMeVC.hidesBottomBarWhenPushed = true
        navigationController?.pushViewController(whoLikedMeVC, animated: true)
    }
}
