//
//  LNHelpFeedbackViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/09.
//

import UIKit
import SnapKit
import JKSwiftExtension

/// 帮助与反馈页面
/// - 文本输入 + 图片选择（相册）+ 提交按钮
/// - iOS 13+ 支持深色模式与动态字体
class LNHelpFeedbackViewController: LNBaseController, UITextViewDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate {

    // 使用白色导航栏
    override var navigationSolidColor: UIColor { return .white }
    override var navigationTitleColor: UIColor { return .label }

    // MARK: - UI
    private lazy var scrollView: UIScrollView = {
        let v = UIScrollView()
        v.alwaysBounceVertical = true
        v.keyboardDismissMode = .interactive
        return v
    }()
    private lazy var contentView = UIView()

    private lazy var textCard: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = s(12)
        v.layer.masksToBounds = true
        return v
    }()

    private lazy var textView: JKPlaceHolderTextView = {
        let tv = JKPlaceHolderTextView()
        tv.font = LNFont.regular(14)
        tv.textColor = UIColor.label
        tv.placeHolder = "Please Enter Feedback Content"
        tv.delegate = self
        tv.isScrollEnabled = false
        return tv
    }()

    private lazy var photoSlot: UIControl = {
        let c = UIControl()
        c.layer.cornerRadius = s(12)
        c.layer.masksToBounds = true
        c.backgroundColor = UIColor.white
        c.addTarget(self, action: #selector(photoTapped), for: .touchUpInside)
        return c
    }()

    private lazy var cameraIcon: UIImageView = {
        let iv = UIImageView(image: UIImage(systemName: "camera"))
        iv.tintColor = UIColor.systemGray
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    private lazy var selectedImageView: UIImageView = {
        let iv = UIImageView()
        iv.isHidden = true
        iv.contentMode = .scaleAspectFill
        iv.clipsToBounds = true
        return iv
    }()

    private lazy var submitButton: UIButton = {
        let b = UIButton(type: .system)
        b.setTitle("Submit", for: .normal)
        b.setTitleColor(.white, for: .normal)
        b.titleLabel?.font = LNFont.regular(16)
        b.layer.cornerRadius = 24
        b.layer.masksToBounds = true
        b.addTarget(self, action: #selector(submitTapped), for: .touchUpInside)
        return b
    }()
    private let submitGradient = CAGradientLayer()

    // MARK: - State
    private var selectedImage: UIImage?

    // MARK: - Life Cycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Help and Feedback"
        view.backgroundColor = UIColor.systemGroupedBackground
        navigationItem.largeTitleDisplayMode = .never
        setupUI()
        setupConstraints()
        setupKeyboardHandling()
        applyGradient()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        submitGradient.frame = submitButton.bounds
    }

    // MARK: - Setup
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        view.addSubview(submitButton)

        contentView.addSubview(textCard)
        textCard.addSubview(textView)

        contentView.addSubview(photoSlot)
        photoSlot.addSubview(selectedImageView)
        photoSlot.addSubview(cameraIcon)

        submitGradient.colors = LNGradient.primaryColors
        submitGradient.startPoint = LNGradient.primaryStartPoint
        submitGradient.endPoint = LNGradient.primaryEndPoint
        submitButton.layer.insertSublayer(submitGradient, at: 0)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalTo(view.safeAreaLayoutGuide)
            make.bottom.equalTo(submitButton.snp.top).offset(-s(16))
        }
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        textCard.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(180)
        }
        textView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(12)
        }

        photoSlot.snp.makeConstraints { make in
            make.top.equalTo(textCard.snp.bottom).offset(s(22))
            make.left.equalTo(textCard)
            make.width.height.equalTo(s(112))
            make.bottom.equalToSuperview().offset(-s(24))
        }
        cameraIcon.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(s(40))
        }
        selectedImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        submitButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(s(24))
            make.height.equalTo(s(48))
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-s(24))
        }
    }

    private func applyGradient() { /* frame 在 viewDidLayoutSubviews 设置 */ }

    // MARK: - Actions
    @objc private func photoTapped() {
        let sheet = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        sheet.addAction(UIAlertAction(title: selectedImage == nil ? "Choose Photo" : "Replace Photo", style: .default, handler: { _ in
            self.pickPhoto()
        }))
        if selectedImage != nil {
            sheet.addAction(UIAlertAction(title: "Remove Photo", style: .destructive, handler: { _ in
                self.selectedImage = nil
                self.selectedImageView.image = nil
                self.selectedImageView.isHidden = true
                self.cameraIcon.isHidden = false
            }))
        }
        sheet.addAction(UIAlertAction(title: "取消", style: .cancel))
        present(sheet, animated: true)
    }

    private func pickPhoto() {
        let picker = UIImagePickerController()
        picker.sourceType = .photoLibrary
        picker.delegate = self
        present(picker, animated: true)
        // 提醒：需在 Info.plist 添加 NSPhotoLibraryUsageDescription
    }

    @objc private func submitTapped() {
        let text = textView.text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else {
            let alert = UIAlertController(title: "提示", message: "请输入反馈内容。", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
            return
        }

        // 模拟提交：压缩图片（如果有），打印字节大小
        var imageSizeInfo = ""
        if let img = selectedImage, let data = img.jpegData(compressionQuality: 0.7) {
            imageSizeInfo = "\nImage: \(data.count) bytes"
        }
        let msg = "感谢你的反馈！\nContent length: \(text.count)\(imageSizeInfo)"
        let alert = UIAlertController(title: "已提交", message: msg, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "好的", style: .default))
        present(alert, animated: true)
        view.endEditing(true)
    }

    // MARK: - UIImagePickerControllerDelegate
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)
        if let img = info[.originalImage] as? UIImage {
            selectedImage = img
            selectedImageView.image = img
            selectedImageView.isHidden = false
            cameraIcon.isHidden = true
        }
    }
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) { picker.dismiss(animated: true) }

    // MARK: - Keyboard handling
    private func setupKeyboardHandling() {
        NotificationCenter.default.addObserver(self, selector: #selector(kbChange(_:)), name: UIResponder.keyboardWillChangeFrameNotification, object: nil)
        let tap = UITapGestureRecognizer(target: self, action: #selector(endEditingTap))
        tap.cancelsTouchesInView = false
        view.addGestureRecognizer(tap)
    }
    @objc private func endEditingTap() { view.endEditing(true) }
    @objc private func kbChange(_ note: Notification) {
        guard let userInfo = note.userInfo,
              let endFrame = (userInfo[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue,
              let duration = userInfo[UIResponder.keyboardAnimationDurationUserInfoKey] as? TimeInterval else { return }
        let insets = max(0, view.bounds.maxY - view.convert(endFrame, from: nil).minY)
        UIView.animate(withDuration: duration) {
            self.scrollView.contentInset.bottom = insets + 12
            self.scrollView.verticalScrollIndicatorInsets.bottom = insets
        }
    }
}

