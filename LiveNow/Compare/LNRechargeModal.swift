//
//  LNRechargeModal.swift
//  LiveNow
//
//  Created by AI Assistant on 2025/8/18.
//

import UIKit
import SnapKit
import JKSwiftExtension

/// 充值弹框模态视图
class LNRechargeModal: UIView {
    
    // MARK: - 数据模型
    struct RechargeItem {
        let type: ItemType
        let amount: String
        let bonus: String?
        let price: String
        let discount: String?
        let icon: String
        
        enum ItemType {
            case diamond
            case goldVIP
            case silverVIP
            case bronzeVIP
        }
    }
    
    // MARK: - 回调
    var onItemSelected: ((RechargeItem) -> Void)?
    var onClose: (() -> Void)?
    
    private var contentH: CGFloat = s(600) + jk_kSafeDistanceBottom
    
    // MARK: - UI Elements
    private lazy var backgroundView: UIView = {
        let view = UIView()
        view.frame = CGRect(x: 0, y: 0, width: jk_kScreenW, height: jk_kScreenH)
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        return view
    }()
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.frame = CGRect(x: 0, y: jk_kScreenH, width: jk_kScreenW, height: contentH)
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var headerView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_charge_alert_bg")
        view.isUserInteractionEnabled(true)
        return view
    }()
    
    private lazy var diamondView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Available Diamonds:"
        label.font = LNFont.bold(s(18))
        label.textColor = .white
        return label
    }()

    private lazy var diamondCountLabel: UILabel = {
        let label = UILabel()
        label.text = "0"
        label.font = LNFont.bold(s(22))
        label.textColor = .white
        return label
    }()
    
    lazy var tableHeadView: UIView = {
        let view = UIView()
        view.frame = CGRect(x: 0, y: 0, width: jk_kScreenW, height: s(20))
        view.jk.addCorner(conrners: [.topLeft, .topRight], radius: s(20))
        view.backgroundColor("#FFFFFF")
        return view
    }()
    
    private lazy var tableView: UITableView = {
        let view = UITableView(frame: .zero, style: .plain)
        view.jk.tableViewNeverAdjustContentInset()
        view.delegate = self
        view.dataSource = self
        view.backgroundColor(.white)
        view.rowHeight(s(70))
        view.separatorStyle = .none
        view.register(LNRechargeItemCell.self, forCellReuseIdentifier: LNRechargeItemCell.className)
        view.tableFooterView = UIView(frame: CGRect(x: 0, y: 0, width: jk_kScreenW, height: jk_kSafeDistanceBottom))
        return view
    }()
    
    
    // MARK: - 数据
    private let rechargeItems: [RechargeItem] = [
        RechargeItem(type: .diamond, amount: "199", bonus: "+100", price: "US$4.99", discount: nil, icon: "💎"),
        RechargeItem(type: .diamond, amount: "199", bonus: "+100", price: "US$4.99", discount: "30%Off", icon: "💎"),
        RechargeItem(type: .goldVIP, amount: "Gold VIP", bonus: "+100", price: "US$4.99", discount: "50%Off", icon: "👑"),
        RechargeItem(type: .silverVIP, amount: "Silver VIP", bonus: "+1000", price: "US$4.99", discount: "50%Off", icon: "🥈"),
        RechargeItem(type: .bronzeVIP, amount: "Bronze VIP", bonus: "+800", price: "US$4.99", discount: "50%Off", icon: "🥉"),
        RechargeItem(type: .diamond, amount: "199", bonus: "+100", price: "US$4.99", discount: nil, icon: "💎")
    ]
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.frame = UIScreen.main.bounds
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 私有方法
    private func setupUI() {
        addSubview(backgroundView)
        addSubview(containerView)

        containerView.addSubview(headerView)
        containerView.addSubview(tableHeadView)
        containerView.addSubview(tableView)

        containerView.addSubview(diamondView)
        diamondView.addSubview(titleLabel)
        diamondView.addSubview(diamondCountLabel)

    }

    private func setupConstraints() {

        // 头部视图
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(s(325))
        }
        tableHeadView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(s(76))
            make.height.equalTo(s(20))
        }
        
        tableView.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.top.equalTo(tableHeadView.snp.bottom)
        }

        // 钻石
        diamondView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(s(39))
        }

        // 标题标签
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(diamondView.snp.leading)
            make.centerY.equalToSuperview()
            make.height.equalTo(s(22))
        }

        // 钻石数量标签
        diamondCountLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel.snp.trailing).offset(s(11))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(26))
            make.trailing.equalToSuperview()
        }

    }

    private func setupActions() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(tapGesture)
    }

    // MARK: - 事件处理
    @objc private func backgroundTapped() {
        dismiss()
    }

    // MARK: - 公共方法
    func show(in parentView: UIView) {
        parentView.addSubview(self)

        // 显示动画
        self.containerView.jk.top = jk_kScreenH
        UIView.animate(withDuration: 0.25) { [weak self] in
            guard let `self` = self else {return}
            self.containerView.jk.top = jk_kScreenH - contentH
        } completion: { (_) in

        }
    }

    func dismiss() {
        
        UIView.animate(withDuration: 0.25) { [weak self] in
            guard let `self` = self else {return}
            self.containerView.jk.top = jk_kScreenH
        } completion: { (_) in
            self.removeFromSuperview()
            self.onClose?()
        }
    }

    func updateDiamondCount(_ count: Int) {
        diamondCountLabel.text = "\(count)"
    }
}

extension LNRechargeModal: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return rechargeItems.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: LNRechargeItemCell = tableView.dequeueReusableCell(withIdentifier: LNRechargeItemCell.className, for: indexPath) as! LNRechargeItemCell
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        
        let item = rechargeItems[indexPath.row]
        onItemSelected?(item)
    }
    
}


class LNRechargeItemCell: UITableViewCell {
    
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        selectionStyle = .none
        self.backgroundColor = .clear
        setupSubviews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupSubviews() {
        
        contentView.addSubview(bgView)
        bgView.addSubview(iconV)
        bgView.addSubview(titleLbl)
        bgView.addSubview(diamondNumLbl)
        bgView.addSubview(diamondIcon)
        bgView.addSubview(priceView)
        priceView.addSubview(priceLbl)
        bgView.addSubview(discountView)
        discountView.addSubview(discountLbl)
        
        bgView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(s(343))
            make.top.equalToSuperview()
            make.bottom.equalTo(s(-10))
        }
        iconV.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(s(9))
            make.size.equalTo(s(32))
        }
        titleLbl.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.leading.equalTo(iconV.snp.trailing).offset(s(7))
            make.height.equalTo(s(20))
        }
        diamondNumLbl.snp.makeConstraints { make in
            make.leading.equalTo(titleLbl.snp.trailing).offset(s(10))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(14))
        }
        diamondIcon.snp.makeConstraints { make in
            make.leading.equalTo(diamondNumLbl.snp.trailing).offset(s(4))
            make.centerY.equalToSuperview()
            make.size.equalTo(s(10))
        }
        priceView.snp.makeConstraints { make in
            make.trailing.equalTo(s(-20))
            make.height.equalTo(s(20))
            make.centerY.equalToSuperview()
        }
        priceLbl.snp.makeConstraints { make in
            make.leading.equalTo(s(6))
            make.trailing.equalTo(s(-6))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(14))
        }
        discountView.snp.makeConstraints { make in
            make.leading.top.equalToSuperview()
            make.height.equalTo(s(18))
        }
        discountLbl.snp.makeConstraints { make in
            make.leading.equalTo(s(6))
            make.trailing.equalTo(s(-6))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(18))
        }
        
    }
    
    // MARK: -
    private lazy var bgView: UIView = {
        let view = UIView()
        view.corner(s(8))
        view.layer.insertSublayer(bgLayer, at: 0)
        return view
    }()
    lazy var bgLayer: CAGradientLayer = {
        let bgLayer = CAGradientLayer()
        
        bgLayer.colors = [UIColor(hexString: "#FFF3FC").cgColor as Any, UIColor(hexString: "#FFFFF0").cgColor as Any]
        bgLayer.locations = [0, 1]
        bgLayer.frame = CGRect(x: 0, y: 0, width: s(343), height: s(60))
        bgLayer.startPoint = CGPoint(x: 0, y: 0.5)
        bgLayer.endPoint = CGPoint(x: 1, y: 0.5)
        return bgLayer
    }()
    private lazy var iconV: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_diamond")
        return view
    }()
    private lazy var titleLbl: UILabel = {
        let view = UILabel()
        view.font(LNFont.medium(16)).color("#151515").text("199")
        return view
    }()
    private lazy var diamondNumLbl: UILabel = {
        let view = UILabel()
        view.font(LNFont.regular(11)).color("#00DFAB").text("100")
        return view
    }()
    private lazy var diamondIcon: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_diamond")
        return view
    }()
    
    private lazy var priceView: UIView = {
        let view = UIView()
        view.corner(s(10)).backgroundColor("#0ADCE1")
        return view
    }()
    
    private lazy var priceLbl: UILabel = {
        let view = UILabel()
        view.font(LNFont.regular(s(12))).color("#FFFFFF").text("$2.99")
        return view
    }()
    
    private lazy var discountView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_discount_bg")
        return view
    }()
    private lazy var discountLbl: UILabel = {
        let view = UILabel()
        view.font(LNFont.regular(11)).color("#000000").text("30%off")
        return view
    }()
    
}
