//
//  LNCompareViewController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/7/28.
//

import UIKit
import SnapKit
import SVGAPlayer
import JKSwiftExtension

/// 匹配页视图控制器 - 雷达匹配功能
class LNCompareViewController: LNBaseController {
    
    override var prefersNavigationBarHide: Bool { return true }

    // MARK: - UI Elements

    /// 渐变背景视图
    private lazy var gradientBackgroundView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_compare_bg")
        view.contentMode(.scaleAspectFill).isUserInteractionEnabled(true)
        return view
    }()

    /// 钻石数量标签
    private lazy var diamondView: UIView = {
        let view = UIView()
        view.corner(s(12))
        view.layer.borderWidth(s(1)).borderColor(UIColor(hexString: "#00DFAB")!)
        let tap = UITapGestureRecognizer(target: self, action: #selector(diamondTap))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    @objc func diamondTap() {
        let vc = LNRechargeViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    private lazy var diamondIcon: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_diamond")
        view.isUserInteractionEnabled(true)
        return view
    }()
    private lazy var diamondLabel: UILabel = {
        let label = UILabel()
        label.text = "0"
        label.font = LNFont.medium(16)
        label.textColor = UIColor(hexString: "#00DFAB")
        return label
    }()

    /// VIP标签
    private lazy var vipBgView: UIImageView = {
        let view = UIImageView()
        view.image = UIImage(named: "ic_compare_vip_bg")
        view.isUserInteractionEnabled(true)
        let tap = UITapGestureRecognizer(target: self, action: #selector(vipTap))
        view.addGestureRecognizer(tap)
        return view
    }()
    
    @objc func vipTap() {
        if let window = JKPOP.keyWindow {
            let alert = LNRechargeModal()
            alert.show(in: window)
        }
    }
    
    private lazy var vipLabel: UILabel = {
        let label = UILabel()
        label.text = "VIP"
        label.font = LNFont.medium(12)
        label.textColor = UIColor(hexString: "#815800")
        return label
    }()

    /// 雷达动画容器
    private lazy var radarContainer: UIView = {
        let view = UIView()
        return view
    }()

    /// SVGA播放器
    private lazy var svgaPlayer: SVGAPlayer = {
        let player = SVGAPlayer()
        player.contentMode = .scaleAspectFit
        player.loops = 0 // 无限循环
        return player
    }()

    /// 中心用户头像
    private lazy var centerAvatarView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "default_avatar") // 默认头像
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 50
        imageView.layer.masksToBounds = true
        imageView.layer.borderWidth = 3
        imageView.layer.borderColor = UIColor.white.cgColor
        return imageView
    }()

    /// 周围的用户头像数组
    private var surroundingAvatars: [UIImageView] = []

    
    /// 倒计时标签
    private lazy var countdownView: UIView = {
        let view = UIView()
        return view
    }()
    private lazy var countdownTipLbl: UILabel = {
        let view = UILabel()
        view.text("Next Free chances:").font(LNFont.medium(16)).color("#151515")
        return view
    }()
    private lazy var countdownLabel: UILabel = {
        let label = UILabel()
        label.text = "01:48:33"
        label.font = LNFont.medium(16)
        label.textColor = UIColor(hexString: "#FF2525")
        return label
    }()

    /// 开始匹配按钮
    private lazy var startMatchingButton: UIButton = {
        let button = UIButton(type: .system)
        button.title("Start matching").font(LNFont.medium(16)).textColor(UIColor(hexString: "#FFFFFF")).corner(s(22)).bgImage(UIImage.jk.gradient(["#04E798", "#0ADCE1"], size: CGSize(width: s(218), height: s(44)), direction: .horizontal))
        return button
    }()

    /// 匹配费用标签
    private lazy var matchCostView: UIView = {
        let view = UIView()
        return view
    }()
    private lazy var diamondImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_diamond")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    private lazy var priceLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.medium(12)
        label.textColor = UIColor(hexString: "#00DFAB")
        label.textAlignment = .left
        label.text("25/match")
        return label
    }()
    private lazy var matchCostLabel: UILabel = {
        let label = UILabel()
        label.text = "Free match once"
        label.font = LNFont.medium(12)
        label.textColor = UIColor(hexString: "#00DFAB")
        label.textAlignment = .center
        label.isHidden(true)
        return label
    }()

    /// 倒计时定时器
    private var countdownTimer: Timer?
    private var remainingSeconds: Int = 6513 // 01:48:33

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
        loadSVGAAnimation()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        startCountdownTimer()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        countdownTimer?.invalidate()
    }

    // MARK: - Private Methods
    private func setupUI() {
        title = "Explore"
        view.backgroundColor = .white

        // 隐藏导航栏
        navigationController?.setNavigationBarHidden(true, animated: false)

        // 添加子视图
        view.addSubview(gradientBackgroundView)
        view.addSubview(diamondView)
        view.addSubview(vipBgView)
        view.addSubview(radarContainer)
        view.addSubview(countdownView)
        countdownView.addSubview(countdownTipLbl)
        countdownView.addSubview(countdownLabel)
        view.addSubview(startMatchingButton)
        view.addSubview(matchCostView)
        matchCostView.addSubview(diamondImageView)
        matchCostView.addSubview(priceLabel)
        view.addSubview(matchCostLabel)

        // 顶部状态栏元素
        diamondView.addSubview(diamondIcon)
        diamondView.addSubview(diamondLabel)
        vipBgView.addSubview(vipLabel)

        // 雷达容器元素
        radarContainer.addSubview(svgaPlayer)
        radarContainer.addSubview(centerAvatarView)
    }

    private func setupConstraints() {
        // 渐变背景
        gradientBackgroundView.snp.makeConstraints { make in
            make.leading.trailing.top.equalToSuperview()
            make.bottom.equalTo(-jk_kTabbarFrameH)
        }

        // 钻石标签
        diamondView.snp.makeConstraints { make in
            make.leading.equalTo(s(15))
            make.top.equalTo(jk_kSafeDistanceTop+s(15))
            make.height.equalTo(s(24))
        }
        diamondIcon.snp.makeConstraints { make in
            make.leading.equalTo(s(7))
            make.centerY.equalToSuperview()
            make.size.equalTo(s(18))
        }
        diamondLabel.snp.makeConstraints { make in
            make.leading.equalTo(diamondIcon.snp.trailing).offset(s(8))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(20))
            make.trailing.equalTo(s(-8))
        }

        // VIP标签
        vipBgView.snp.makeConstraints { make in
            make.trailing.equalTo(s(-15))
            make.centerY.equalTo(diamondView.snp.centerY)
            make.size.equalTo(CGSize(width: s(52), height: s(24)))
        }
        vipLabel.snp.makeConstraints { make in
            make.trailing.equalTo(-s(7))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(14))
        }

        // 雷达容器
        radarContainer.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(diamondView.snp.bottom).offset(s(70))
            make.size.equalTo(CGSize(width: s(281), height: s(277)))
        }

        // SVGA播放器
        svgaPlayer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 中心头像
        centerAvatarView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(s(90))
        }

        // 倒计时标签
        countdownView.snp.makeConstraints { make in
            make.top.equalTo(radarContainer.snp.bottom).offset(s(21))
            make.centerX.equalToSuperview()
        }
        countdownTipLbl.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.height.equalTo(s(20))
            make.centerY.equalToSuperview()
        }
        countdownLabel.snp.makeConstraints { make in
            make.leading.equalTo(countdownTipLbl.snp.trailing).offset(s(8))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(20))
            make.trailing.equalToSuperview()
        }

        // 开始匹配按钮
        startMatchingButton.snp.makeConstraints { make in
            make.bottom.equalTo(matchCostView.snp.top).offset(s(-8))
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSize(width: s(218), height: s(44)))
        }

        // 匹配费用标签
        matchCostView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-s(48)-jk_kTabbarFrameH)
            make.height.equalTo(s(18))
        }
        diamondImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.centerY.equalToSuperview()
            make.size.equalTo(s(18))
        }
        priceLabel.snp.makeConstraints { make in
            make.leading.equalTo(diamondImageView.snp.trailing).offset(s(8))
            make.centerY.equalToSuperview()
            make.height.equalTo(s(14))
            make.trailing.equalToSuperview()
        }
        
        matchCostLabel.snp.makeConstraints { make in
            make.bottom.equalTo(-s(48)-jk_kTabbarFrameH)
            make.centerX.equalToSuperview()
        }
    }

    private func setupActions() {
        startMatchingButton.addTarget(self, action: #selector(startMatchingTapped), for: .touchUpInside)
    }

    private func loadSVGAAnimation() {
        // 这里应该加载实际的SVGA雷达动画文件
        // 由于没有实际的SVGA文件，我们创建一个简单的动画效果
        createRadarAnimation()
    }

    private func createRadarAnimation() {
        // 创建雷达扫描动画效果
        let radarView = UIView()
        radarView.backgroundColor = .clear
        radarContainer.insertSubview(radarView, at: 0)
        radarView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 添加同心圆
        for i in 1...4 {
            let circleView = UIView()
            circleView.backgroundColor = .clear
            circleView.layer.borderWidth = 1
            circleView.layer.borderColor = UIColor.white.withAlphaComponent(0.3).cgColor
            circleView.layer.cornerRadius = CGFloat(i * 40)

            radarView.addSubview(circleView)
            circleView.snp.makeConstraints { make in
                make.center.equalToSuperview()
                make.width.height.equalTo(i * 80)
            }
        }

        // 添加扫描线动画
        let scanLine = UIView()
        scanLine.backgroundColor = UIColor.white.withAlphaComponent(0.5)
        radarView.addSubview(scanLine)
        scanLine.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(2)
            make.height.equalTo(175)
        }

        // 旋转动画
        let rotationAnimation = CABasicAnimation(keyPath: "transform.rotation")
        rotationAnimation.fromValue = 0
        rotationAnimation.toValue = Double.pi * 2
        rotationAnimation.duration = 3.0
        rotationAnimation.repeatCount = .infinity
        scanLine.layer.add(rotationAnimation, forKey: "rotation")
    }

    private func startCountdownTimer() {
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateCountdown()
        }
    }

    private func updateCountdown() {
        guard remainingSeconds > 0 else {
            countdownTimer?.invalidate()
            countdownLabel.text = "00:00:00"
            return
        }

        remainingSeconds -= 1
        let hours = remainingSeconds / 3600
        let minutes = (remainingSeconds % 3600) / 60
        let seconds = remainingSeconds % 60

        countdownLabel.text = String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }

    @objc private func startMatchingTapped() {
        // 添加按钮点击动画
        UIView.animate(withDuration: 0.1, animations: {
            self.startMatchingButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.startMatchingButton.transform = .identity
            }
        }

        // 这里可以添加实际的匹配逻辑
//        let alert = UIAlertController(title: "开始匹配", message: "匹配功能正在开发中，敬请期待！", preferredStyle: .alert)
//        alert.addAction(UIAlertAction(title: "确定", style: .default))
//        present(alert, animated: true)
        
        if let window = JKPOP.keyWindow {
            let alert = LNFreeChanceModal()
            alert.show(in: window)
        }
        
    }

    deinit {
        countdownTimer?.invalidate()
    }
}
