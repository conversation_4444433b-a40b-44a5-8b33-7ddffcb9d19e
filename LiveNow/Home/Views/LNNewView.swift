//
//  LNNewView.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit
import SnapKit
import JKSwiftExtension
import JXSegmentedView
import MJRefresh

/// New 页面视图 - 展示最新直播内容
class LNNewView: UIView {

    // MARK: - UI Elements
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = s(10)
        layout.minimumInteritemSpacing = s(15)
        layout.sectionInset = UIEdgeInsets(top: s(10), left: s(15), bottom: s(10), right: s(15))


        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LNLiveStreamCell.self, forCellWithReuseIdentifier: "LNLiveStreamCell")
        return collectionView
    }()

    // MARK: - Data
    private var anchors: [LNAnchorModel] = []
    private lazy var viewModel = LNAnchorListViewModel(type: .new)
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupViewModel()
        setupRefresh()
        loadData()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Private Methods
    private func setupUI() {
        backgroundColor = .clear
        addSubview(collectionView)
    }

    private func setupConstraints() {
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupViewModel() {
        viewModel.delegate = self
    }

    private func setupRefresh() {
        // 下拉刷新
        collectionView.mj_header = MJRefreshNormalHeader { [weak self] in
            self?.loadData(refresh: true)
        }

        // 上拉加载更多
        collectionView.mj_footer = MJRefreshAutoNormalFooter { [weak self] in
            self?.loadMore()
        }
    }

    private func loadData(refresh: Bool = true) {
        if refresh {
            collectionView.mj_footer?.resetNoMoreData()
        }
        viewModel.loadData(refresh: refresh)
    }

    private func loadMore() {
        viewModel.loadMore()
    }


}

// MARK: - UICollectionViewDataSource
extension LNNewView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return anchors.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LNLiveStreamCell", for: indexPath) as! LNLiveStreamCell
        cell.configure(with: anchors[indexPath.item])
        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension LNNewView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (kscreenW - s(45)) / 2
        let height = s(280)
        return CGSize(width: width, height: height)
    }
}

// MARK: - UICollectionViewDelegate
extension LNNewView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let anchor = anchors[indexPath.item]
        print("选中了直播间: \(anchor.displayName)")
        // TODO: 跳转到直播间详情页
    }
}

// MARK: - LNAnchorListViewModelDelegate
extension LNNewView: LNAnchorListViewModelDelegate {
    func dataDidLoad(anchors: [LNAnchorModel]) {
        self.anchors = anchors
        collectionView.reloadData()

        // 结束刷新状态
        collectionView.mj_header?.endRefreshing()

        // 处理加载更多状态
        if viewModel.getHasMore() {
            collectionView.mj_footer?.endRefreshing()
        } else {
            collectionView.mj_footer?.endRefreshingWithNoMoreData()
        }
    }

    func dataDidFail(error: Error) {
        // 结束刷新状态
        collectionView.mj_header?.endRefreshing()
        collectionView.mj_footer?.endRefreshing()

        // 显示错误信息
        print("加载最新主播失败: \(error.localizedDescription)")
        // TODO: 显示错误提示
    }

    func loadingStateChanged(isLoading: Bool) {
        // 可以在这里显示/隐藏加载指示器
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate
extension LNNewView: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return self
    }
}
