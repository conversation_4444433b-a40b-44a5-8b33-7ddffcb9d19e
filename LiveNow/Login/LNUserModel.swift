//
//  LNUserModel.swift
//  LiveNow
//
//  Created by pp on 2025/8/13.
//

import UIKit


class LNUserModel: BaseModel {
    var age: Int = 0
    var agoraRtcAppId: String = ""
    var anchorFileList: [String] = []
    var anchorLabelList: [String] = []
    var auditFlag: String = ""
    var birthday: String = ""
    var constellation: Int = 0  // 更新为Int类型，匹配API返回
    var country: String = ""
    var coverVideoUrl: String = ""
    var currentGradeStd: Int = 0
    var dayFreeFreshTime: String = ""
    var dayFreeFreshTimeStamp: String = "-1"
    var diamond: Int = 0
    var diamondTotal: String = "-1"
    var email: String = ""
    var facebookClientToken: String = ""
    var facebookId: String = ""
    var fansNum: Int = 0
    var firstFlag: Int = 1
    var firstRecharge: Int = 1
    var firstRechargeExpireTime: String = ""
    var followNum: Int = 0
    var followFlag: String = ""
    var freeRandomMatch: Int = 0
    var freeVideoCall: Int = 0
    var gender: String = ""  // 更新为String类型，匹配API返回
    var giveDiamond: Int = 0
    var groundFileName: String = ""  // 新增字段
    var hasGoogleRelation: String = ""
    var hasThirdPay: String = ""
    var headFileName: String = ""
    var height: String = ""
    var hotAnchor: String = ""  // 新增字段
    var id: Int = 0
    var incomeDiamond: Int = 0
    var incomeDiamondDecimal: String = "-1"
    var initialFreeRandomMatch: Int = 2
    var initialFreeVideoCall: Int = 0
    var initialFreeVideoPlay: String = "-1"
    var isNeedMute: Bool = true  // 新增字段
    var isTop: String = ""  // 新增字段
    var isVirVideo: String = ""  // 新增字段
    var language: String = ""  // 新增字段（单数形式）
    var languages: [String] = []
    var lastIp: String = ""
    var level: String = ""
    var levelKey: Int = 0
    var msgSendNum: Int = 0
    var needMuteStr: String = ""  // 新增字段
    var nextGradeDiff: Int = 0
    var nextGradeStd: Int = 0
    var nickName: String = ""
    var normalHeadFileName: String = ""  // 新增字段
    var onlineStatus: String = ""
    var payInfoStatus: String = ""
    var phone: String = ""
    var photos: [String] = []
    var power: String = ""
    var remark: String = ""
    var repeatId: Int = 0  // 新增字段
    var rongCloudAppKey: String = ""
    var roomTitle: String = ""  // 新增字段
    var showVideoUrl: String = ""
    var singleRecharge: Int = 0
    var source: Int = 0  // 新增字段
    var status: String = ""
    var signature: String = ""
    var token: String = ""
    var trends: [Any] = []
    var trendsNum: String = "-1"
    var unionId: Int = 0
    var userCategory: String = ""
    var userCode: String = ""
    var userRole: Int = 0
    var username: String = ""
    var videoPrice: Int = 0
    var vipExpireDay: String = ""
    var vipExpireFlag: Int = 0
    var vipVideoPrice: Int = 0
    var vipFlag: String = ""
    var virVideoId: Int = 0  // 新增字段
    var weight: String = ""
    var imToken: String = "" //自定义字段
}



