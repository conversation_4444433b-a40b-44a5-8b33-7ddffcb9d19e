//
//  LNCompleteDataViewController.swift
//  LiveNow
//
//  Created by edy on 2025/8/18.
//

import UIKit
import SnapKit
import JKSwiftExtension

class LNCompleteDataViewController: LNBaseController {
    
    // MARK: - Properties
    private var selectedGender: String = "Boy"
    private var selectedDate: Date = Date()
    private var selectedCountry: LNCountry? = nil
    private var selectedCountryFlag: String = "🇸🇦"
    
    // MARK: - Override Properties
    override var prefersNavigationBarHide: Bool { return true }
    override var preferredStatusBarStyle: UIStatusBarStyle { return .lightContent }
    override var navigationTitleColor: UIColor { return .white }
    override var navigationSolidColor: UIColor { return .clear }
    
    // MARK: - UI Components
    private lazy var navView: UIView = {
        let view = UIView()
        view.backgroundColor(.clear)
        return view
    }()
    private lazy var backBtn: UIButton = {
        let view = UIButton(type: .custom)
        view.image(UIImage(named: "ic_nav_back"))
        view.addTarget(self, action: #selector(backBtnAction), for: .touchUpInside)
        return view
    }()
    private lazy var navTitleLbl: UILabel = {
        let view = UILabel()
        view.text("Complete data").color("#FFFFFF").font(LNFont.bold(18))
        return view
    }()
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.backgroundColor = .clear
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var avatarContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        imageView.layer.cornerRadius = s(39)
        imageView.layer.masksToBounds = true
        imageView.contentMode = .scaleAspectFill
        imageView.isUserInteractionEnabled = true
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
        imageView.addGestureRecognizer(tapGesture)
        
        return imageView
    }()
    
    private lazy var cameraImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_camera_icon")
        imageView.contentMode = .scaleAspectFit
        imageView.isUserInteractionEnabled = true
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.text = "Name Taken"
        label.font = LNFont.medium(16)
        label.textColor = .white
        return label
    }()
    
    private lazy var nameTextField: UITextField = {
        let textField = UITextField()
        textField.backgroundColor = .white
        textField.layer.cornerRadius = s(21)
        textField.layer.masksToBounds = true
        textField.font = LNFont.regular(16)
        textField.textColor = .black
        textField.text = "Gentle Rose"
        textField.textAlignment = .center
        
        // 设置左边距
        let leftView = UIView(frame: CGRect(x: 0, y: 0, width: s(20), height: s(50)))
        textField.leftView = leftView
        textField.leftViewMode = .always
        
        // 设置右边图标
        let rightView = UIView(frame: CGRect(x: 0, y: 0, width: s(50), height: s(50)))
        let iconImageView = UIImageView(frame: CGRect(x: s(15), y: s(15), width: s(20), height: s(20)))
        iconImageView.image = UIImage(named: "ic_name_change")
        rightView.addSubview(iconImageView)
        textField.rightView = rightView
        textField.rightViewMode = .always
        
        return textField
    }()
    
    private lazy var genderLabel: UILabel = {
        let label = UILabel()
        label.text = "Choose Gender"
        label.font = LNFont.medium(16)
        label.textColor = .white
        return label
    }()
    
    private lazy var genderContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.layer.cornerRadius = s(21)
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var boyButton: UIButton = {
        let button = UIButton(type: .custom)
        button.title("Boy").image(UIImage(named: "ic_gender_male")).textColor(UIColor(hexString: "#F5F5F5")!).textColor(UIColor(hexString: "#151515")!, .selected).font(LNFont.regular(16)).bgImage(UIImage.jk.image(color: UIColor(hexString: "#FFFFFF")!, size: CGSize(width: (jk_kScreenW-s(88))/2.0, height: s(42))), .selected).bgImage(UIImage.jk.image(color: UIColor(hexString: "#FFFFFF", alpha: 0.6)!, size: CGSize(width: (jk_kScreenW-s(88))/2.0, height: s(42))))
        button.jk.setImageTitleLayout(.imgRight, spacing: s(65))
        
        button.addTarget(self, action: #selector(genderButtonTapped(_:)), for: .touchUpInside)
        button.tag = 0
        return button
    }()
    
    private lazy var girlButton: UIButton = {
        let button = UIButton(type: .custom)
        button.title("Girl").image(UIImage(named: "ic_gender_female")).textColor(UIColor(hexString: "#F5F5F5")!).textColor(UIColor(hexString: "#151515")!, .selected).font(LNFont.regular(16)).bgImage(UIImage.jk.image(color: UIColor(hexString: "#FFFFFF")!, size: CGSize(width: (jk_kScreenW-s(88))/2.0, height: s(42))), .selected).bgImage(UIImage.jk.image(color: UIColor(hexString: "#FFFFFF", alpha: 0.6)!, size: CGSize(width: (jk_kScreenW-s(88))/2.0, height: s(42))))
        button.jk.setImageTitleLayout(.imgRight, spacing: s(65))
        button.addTarget(self, action: #selector(genderButtonTapped(_:)), for: .touchUpInside)
        button.tag = 1
        return button
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        updateGenderSelection()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        setupGradientBackground()
    }
    
    // MARK: - Setup Methods
    private func setupNavigationBar() {
        title = "Complete Data"
        
        // 设置返回按钮
        let backButton = UIBarButtonItem(
            image: UIImage(systemName: "chevron.left"),
            style: .plain,
            target: self,
            action: #selector(backButtonTapped)
        )
        backButton.tintColor = .white
        navigationItem.leftBarButtonItem = backButton
    }
    
    private func setupGradientBackground() {
        
        // 移除之前的渐变层
        view.layer.sublayers?.removeAll { $0 is CAGradientLayer }

        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor(hexString: "#00FFB7").cgColor,
            UIColor(hexString: "#2ADFD0").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0.5, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0.5, y: 1)
        gradientLayer.frame = view.bounds
        view.layer.insertSublayer(gradientLayer, at: 0)
    }

    // MARK: - Additional UI Components
    private lazy var birthdayLabel: UILabel = {
        let label = UILabel()
        label.text = "Choose Birthday"
        label.font = LNFont.medium(16)
        label.textColor = .white
        return label
    }()

    private lazy var birthdayButton: UIButton = {
        let button = UIButton(type: .custom)
        button.backgroundColor = .white
        button.layer.cornerRadius = s(25)
        button.layer.masksToBounds = true
        button.contentHorizontalAlignment = .left
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: s(20), bottom: 0, right: s(50))
        button.addTarget(self, action: #selector(birthdayButtonTapped), for: .touchUpInside)

        // 设置标题
        button.setTitle("1998-08-08", for: .normal)
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = LNFont.regular(16)

        // 添加右侧箭头
        let arrowImageView = UIImageView()
        arrowImageView.image = UIImage(systemName: "chevron.right")
        arrowImageView.tintColor = .gray
        arrowImageView.contentMode = .scaleAspectFit
        button.addSubview(arrowImageView)
        arrowImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(20))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(16))
        }

        return button
    }()
    
    @objc func backBtnAction() {
        navigationController?.popViewController(animated: true)
    }

    private lazy var countryLabel: UILabel = {
        let label = UILabel()
        label.text = "Country Selection"
        label.font = LNFont.medium(16)
        label.textColor = .white
        return label
    }()

    private lazy var countryButton: UIButton = {
        let button = UIButton(type: .custom)
        button.backgroundColor = .white
        button.layer.cornerRadius = s(21)
        button.layer.masksToBounds = true
        button.contentHorizontalAlignment = .left
        button.titleEdgeInsets = UIEdgeInsets(top: 0, left: s(20), bottom: 0, right: s(80))
        button.addTarget(self, action: #selector(countryButtonTapped), for: .touchUpInside)

        // 设置标题
        button.setTitle("My Country", for: .normal)
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = LNFont.regular(16)

        // 添加国旗
        let flagLabel = UILabel()
        flagLabel.text = "🇸🇦"
        flagLabel.font = LNFont.regular(20)
        button.addSubview(flagLabel)
        flagLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(50))
            make.centerY.equalToSuperview()
        }

        // 添加右侧箭头
        let arrowImageView = UIImageView()
        arrowImageView.image = UIImage(systemName: "chevron.right")
        arrowImageView.tintColor = .gray
        arrowImageView.contentMode = .scaleAspectFit
        button.addSubview(arrowImageView)
        arrowImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(20))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(16))
        }

        return button
    }()

    private lazy var goButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("GO!", for: .normal)
        button.titleLabel?.font = LNFont.bold(24)
        button.setTitleColor(UIColor.hex(hexString: "#00E5A0"), for: .normal)
        button.backgroundColor = .white
        button.layer.cornerRadius = s(35)
        button.layer.masksToBounds = true
        button.addTarget(self, action: #selector(goButtonTapped), for: .touchUpInside)

        // 添加阴影
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 4)
        button.layer.shadowOpacity = 0.1
        button.layer.shadowRadius = 8
        button.layer.masksToBounds = false

        return button
    }()

    // MARK: - Setup UI
    private func setupUI() {
        view.addSubview(scrollView)
        view.addSubview(navView)
        navView.addSubview(backBtn)
        navView.addSubview(navTitleLbl)
        scrollView.addSubview(contentView)

        // 添加头像相关视图
        contentView.addSubview(avatarContainerView)
        avatarContainerView.addSubview(avatarImageView)
        avatarContainerView.addSubview(cameraImageView)

        // 添加表单元素
        contentView.addSubview(nameLabel)
        contentView.addSubview(nameTextField)
        contentView.addSubview(genderLabel)
        contentView.addSubview(genderContainerView)

        // 性别选择按钮
        genderContainerView.addSubview(boyButton)
        genderContainerView.addSubview(girlButton)

        // 生日和国家选择
        contentView.addSubview(birthdayLabel)
        contentView.addSubview(birthdayButton)
        contentView.addSubview(countryLabel)
        contentView.addSubview(countryButton)

        // GO按钮和底部指示器
        contentView.addSubview(goButton)
    }

    private func setupConstraints() {
        
        navView.snp.makeConstraints { make in
            make.leading.top.trailing.equalToSuperview()
            make.height.equalTo(jk_kNavFrameH)
        }
        backBtn.snp.makeConstraints { make in
            make.leading.equalTo(s(16))
            make.bottom.equalTo(s(-10))
            make.size.equalTo(s(24))
        }
        navTitleLbl.snp.makeConstraints { make in
            make.centerY.equalTo(backBtn.snp.centerY)
            make.height.equalTo(s(20))
            make.centerX.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.leading.bottom.trailing.equalToSuperview()
            make.top.equalTo(navView.snp.bottom)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
            make.height.greaterThanOrEqualToSuperview()
        }

        // 头像容器
        avatarContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(40))
            make.centerX.equalToSuperview()
            make.width.height.equalTo(s(86))
        }

        // 头像
        avatarImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(s(78))
        }

        // 相机图标
        cameraImageView.snp.makeConstraints { make in
            make.bottom.right.equalTo(avatarContainerView)
            make.width.height.equalTo(s(28))
        }

        // 姓名标签
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarContainerView.snp.bottom).offset(s(25))
            make.left.equalToSuperview().offset(s(58))
        }

        // 姓名输入框
        nameTextField.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(s(8))
            make.left.right.equalToSuperview().inset(s(44))
            make.height.equalTo(s(42))
        }

        // 性别标签
        genderLabel.snp.makeConstraints { make in
            make.top.equalTo(nameTextField.snp.bottom).offset(s(18))
            make.left.equalToSuperview().offset(s(58))
        }

        // 性别容器
        genderContainerView.snp.makeConstraints { make in
            make.top.equalTo(genderLabel.snp.bottom).offset(s(8))
            make.left.right.equalToSuperview().inset(s(44))
            make.height.equalTo(s(42))
        }

        // Boy按钮
        boyButton.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo((jk_kScreenW-s(88))/2.0)
            make.height.equalTo(s(42))
        }

        // Girl按钮
        girlButton.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo((jk_kScreenW-s(88))/2.0)
            make.height.equalTo(s(42))
        }

        // 生日标签
        birthdayLabel.snp.makeConstraints { make in
            make.top.equalTo(genderContainerView.snp.bottom).offset(s(18))
            make.left.equalToSuperview().offset(s(58))
        }

        // 生日按钮
        birthdayButton.snp.makeConstraints { make in
            make.top.equalTo(birthdayLabel.snp.bottom).offset(s(12))
            make.left.right.equalToSuperview().inset(s(44))
            make.height.equalTo(s(42))
        }

        // 国家标签
        countryLabel.snp.makeConstraints { make in
            make.top.equalTo(birthdayButton.snp.bottom).offset(s(18))
            make.left.equalToSuperview().offset(s(58))
        }

        // 国家按钮
        countryButton.snp.makeConstraints { make in
            make.top.equalTo(countryLabel.snp.bottom).offset(s(12))
            make.left.right.equalToSuperview().inset(s(44))
            make.height.equalTo(s(42))
        }

        // GO按钮
        goButton.snp.makeConstraints { make in
            make.top.equalTo(countryButton.snp.bottom).offset(s(55))
            make.centerX.equalToSuperview()
            make.width.height.equalTo(s(70))
            make.bottom.equalToSuperview().offset(-s(40)-jk_kSafeDistanceBottom)
        }
    }

    // MARK: - Event Handlers
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }

    @objc private func avatarTapped() {
        // 显示头像选择弹框
        let avatarModal = LNAvatarModal()
        avatarModal.onSelectFromAlbum = { [weak self] in
            self?.selectFromPhotoLibrary()
        }
        avatarModal.onTakePhoto = { [weak self] in
            self?.takePhoto()
        }
        avatarModal.show(in: self.view)
    }

    @objc private func genderButtonTapped(_ sender: UIButton) {
        selectedGender = sender.tag == 0 ? "Boy" : "Girl"
        updateGenderSelection()
    }

    @objc private func birthdayButtonTapped() {
        let dateModal = LNDateOfBirthModal()
        dateModal.onConfirm = { [weak self] date in
            self?.selectedDate = date
            self?.updateBirthdayDisplay()
        }
        dateModal.show(in: self.view, initialDate: selectedDate)
    }

    @objc private func countryButtonTapped() {
        let countryModal = LNCountryModal()
        countryModal.onCountrySelected = { [weak self] country in
            self?.selectedCountry = country
            self?.updateCountryDisplay()
        }
        countryModal.show(in: self.view)
    }

    @objc private func goButtonTapped() {
        // 验证数据
        guard let name = nameTextField.text, !name.isEmpty else {
            showAlert(message: "Please enter your name")
            return
        }

        // 收集所有数据
        let userData: [String: Any] = [
            "name": name,
            "gender": selectedGender,
            "birthday": formatDate(selectedDate),
            "country": selectedCountry?.name ?? "My Country",
            "countryCode": selectedCountry?.code ?? "",
            "countryFlag": selectedCountryFlag
        ]

        print("User Data: \(userData)")

        // 这里可以添加数据保存或提交逻辑
        showAlert(message: "Data completed successfully!") {
            self.navigationController?.popViewController(animated: true)
        }
    }

    // MARK: - Helper Methods
    private func updateGenderSelection() {
        if selectedGender == "Boy" {
            boyButton.isSelected = true
            girlButton.isSelected = false
            
        } else {
            boyButton.isSelected = false
            girlButton.isSelected = true
        }
    }

    private func updateBirthdayDisplay() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        birthdayButton.setTitle(formatter.string(from: selectedDate), for: .normal)
    }

    private func updateCountryDisplay() {
        let countryName = selectedCountry?.name ?? "My Country"
        countryButton.setTitle(countryName, for: .normal)

    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: date)
    }

    private func showAlert(message: String, completion: (() -> Void)? = nil) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            completion?()
        })
        present(alert, animated: true)
    }

    // MARK: - Photo Selection
    private func selectFromPhotoLibrary() {
        // 这里可以添加从相册选择照片的逻辑
        print("Select from photo library")
    }

    private func takePhoto() {
        // 这里可以添加拍照的逻辑
        print("Take photo")
    }
}
