//
//  LNBaseController.swift
//  LiveNow
//
//  Created by edy on 2025/8/12.
//

import UIKit
import SnapKit

class LNBaseController: UIViewController {
    
    /// 是否隐藏导航
    var prefersNavigationBarHide: Bool { get { return false } }
    
    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .default
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hexString: "#F5F5F5")

        self.edgesForExtendedLayout = .bottom
        // 字体在代码处已显式指定 LNFont，不再递归替换

        // 设置圆弧形渐变背景
        setupArcGradientBackgroundIfNeeded()

        // 设置顶部背景
        setupTopBackgroundIfNeeded()

        // Do any additional setup after loading the view.
        if let nav = navigationController, nav.viewControllers.count > 1, !self.isBeingPresented {
            navigationItem.leftBarButtonItem = LNNav.makeBackBarButtonItem(target: self, action: #selector(onBackButtonItem))
        }
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        setNeedsNavigationBarAppearanceUpdate(animated)
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // 更新圆弧形渐变背景
        updateArcGradientBackgroundIfNeeded()

        // 更新顶部背景
        updateTopBackgroundIfNeeded()
    }

    @objc public func onBackButtonItem() {
        navigationController?.popViewController(animated: true)
    }
    
    func setNeedsNavigationBarAppearanceUpdate(_ animated: Bool) {
        let hide = prefersNavigationBarHide
        navigationController?.setNavigationBarHidden(hide, animated: animated)
        applyNavigationAppearance()
        // 同步自定义导航控制器的背景隐藏/显示
        if let lnNav = navigationController as? LNNavigationViewController {
            lnNav.applyBackground(from: self)
        }
    }

    // MARK: - 子类可覆盖的导航栏样式
    /// 非渐变时的纯色背景，默认白色
    @objc dynamic var navigationSolidColor: UIColor { return .white }
    /// 标题颜色
    @objc dynamic var navigationTitleColor: UIColor { return .label }
    /// 阴影线是否隐藏
    @objc dynamic var navigationShadowHidden: Bool { return true }

    // MARK: - 子类可覆盖的顶部背景样式
    /// 是否使用顶部渐变背景。默认 false
    @objc dynamic var useTopGradientBackground: Bool { return false }
    /// 顶部渐变背景颜色数组，默认 #D7FFF1 -> #EFFFEA
    @objc dynamic var topGradientColors: [UIColor] {
        return [
            UIColor.hex(hexString: "#D7FFF1"),
            UIColor.hex(hexString: "#EFFFEA")
        ]
    }

    /// 是否使用顶部白色背景。默认 false
    @objc dynamic var useTopWhiteBackground: Bool { return false }

    // MARK: - 子类可覆盖的圆弧形渐变背景样式
    /// 是否使用圆弧形渐变背景。默认 false
    @objc dynamic var useArcGradientBackground: Bool { return false }
    /// 圆弧形渐变背景颜色数组，默认 #B8FFE6 -> #E2FFD9
    @objc dynamic var arcGradientColors: [UIColor] {
        return [
            UIColor.hex(hexString: "#B8FFE6"),
            UIColor.hex(hexString: "#E2FFD9")
        ]
    }
    /// 圆弧形背景高度，默认 280pt
    @objc dynamic var arcGradientBackgroundHeight: CGFloat { return s(280) }

    private func applyNavigationAppearance() {
        guard let nav = navigationController else { return }
        let appearance = UINavigationBarAppearance()
        // 让导航栏完全透明，把背景交给自定义导航控制器的背景视图统一绘制（包含状态栏区域），避免色差/分层
        appearance.configureWithTransparentBackground()
        appearance.backgroundColor = .clear

        appearance.titleTextAttributes = [
            .font: LNFont.medium(18),
            .foregroundColor: navigationTitleColor
        ]
        appearance.shadowColor = navigationShadowHidden ? .clear : UIColor.separator

        nav.navigationBar.standardAppearance = appearance
        nav.navigationBar.scrollEdgeAppearance = appearance
        nav.navigationBar.compactAppearance = appearance
        nav.navigationBar.isTranslucent = true
        nav.view.setNeedsLayout()
        nav.view.layoutIfNeeded()

        // 背景绘制交由自定义导航控制器在切换/展示时统一处理
    }

    // MARK: - 顶部背景

    // 顶部渐变背景视图
    private lazy var topGradientView: UIView = {
        let view = UIView()
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = topGradientColors.map { $0.cgColor }
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        view.layer.addSublayer(gradientLayer)
        view.tag = 999 // 用于在layoutSubviews中更新frame
        return view
    }()

    // 顶部白色背景视图
    private lazy var topWhiteView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // MARK: - 圆弧形渐变背景

    // 圆弧形渐变背景视图
    private lazy var arcGradientBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 圆弧形渐变层
    private lazy var arcGradientLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.startPoint = CGPoint(x: 0, y: 0)
        layer.endPoint = CGPoint(x: 1, y: 0)
        return layer
    }()

    // 圆弧形遮罩层
    private lazy var arcMaskLayer: CAShapeLayer = {
        let layer = CAShapeLayer()
        layer.fillColor = UIColor.black.cgColor
        return layer
    }()

    private func setupTopBackgroundIfNeeded() {
        if useTopGradientBackground {
            // 添加顶部渐变背景视图
            view.addSubview(topGradientView)
            topGradientView.snp.makeConstraints { make in
                make.top.left.right.equalToSuperview()
                make.height.equalTo(knavH)
            }
        } else if useTopWhiteBackground {
            // 添加顶部白色背景视图
            view.addSubview(topWhiteView)
            topWhiteView.snp.makeConstraints { make in
                make.top.left.right.equalToSuperview()
                make.height.equalTo(knavH)
            }
        }
    }

    private func updateTopBackgroundIfNeeded() {
        if useTopGradientBackground {
            // 更新渐变层的frame
            if let gradientLayer = topGradientView.layer.sublayers?.first as? CAGradientLayer {
                gradientLayer.frame = topGradientView.bounds
            }
        }
        // 白色背景不需要特殊更新
    }

    private func setupArcGradientBackgroundIfNeeded() {
        guard useArcGradientBackground else { return }

        // 添加圆弧形渐变背景到视图
        view.addSubview(arcGradientBackgroundView)

        // 设置约束
        arcGradientBackgroundView.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.right.equalToSuperview()
            make.height.equalTo(arcGradientBackgroundHeight)
        }

        // 更新渐变颜色
        arcGradientLayer.colors = arcGradientColors.map { $0.cgColor }
    }

    private func updateArcGradientBackgroundIfNeeded() {
        guard useArcGradientBackground else { return }

        let backgroundFrame = arcGradientBackgroundView.bounds
        guard backgroundFrame.width > 0 && backgroundFrame.height > 0 else { return }

        // 设置渐变层frame
        arcGradientLayer.frame = backgroundFrame

        // 创建圆弧形路径
        let path = UIBezierPath()
        let width = backgroundFrame.width
        let height = backgroundFrame.height

        // 从左上角开始
        path.move(to: CGPoint(x: 0, y: 0))
        // 到右上角
        path.addLine(to: CGPoint(x: width, y: 0))
        // 到右下角，但是要创建圆弧
        path.addLine(to: CGPoint(x: width, y: height * 0.7))

        // 创建圆弧曲线到左下角
        let controlPoint1 = CGPoint(x: width * 0.7, y: height * 0.9)
        let controlPoint2 = CGPoint(x: width * 0.3, y: height * 1.1)
        let endPoint = CGPoint(x: 0, y: height * 0.8)
        path.addCurve(to: endPoint, controlPoint1: controlPoint1, controlPoint2: controlPoint2)

        // 回到起点
        path.addLine(to: CGPoint(x: 0, y: 0))
        path.close()

        // 设置遮罩层路径
        arcMaskLayer.path = path.cgPath

        // 如果还没有添加渐变层，则添加
        if arcGradientLayer.superlayer == nil {
            arcGradientBackgroundView.layer.addSublayer(arcGradientLayer)
        }

        // 设置遮罩
        arcGradientLayer.mask = arcMaskLayer
    }

}
